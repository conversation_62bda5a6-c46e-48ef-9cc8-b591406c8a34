// 练习13：创建一个类，它包含一个被重载了三次的方法。继承产生一个新类，
// 并添加一个该方法的新的重载定义。展示这四个方法在导出类中都是可以使用的。

package chapter7;

class ThreeOverloads {
    public void f(int i) {
        System.out.println("f(int): " + i);
    }
    
    public void f(char c) {
        System.out.println("f(char): " + c);
    }
    
    public void f(double d) {
        System.out.println("f(double): " + d);
    }
}

class AddOverload extends ThreeOverloads {
    public void f(String s) {
        System.out.println("f(String): " + s);
    }
}

public class Exercise713 {
    public static void main(String[] args) {
        AddOverload ao = new AddOverload();
        ao.f(1);      // int version
        ao.f('a');    // char version
        ao.f(1.1);    // double version
        ao.f("hello"); // String version
    }
} 