/**
 * 练习20：创建一个包含可变参数列表的main()方法，
 * 可以用各种不同数量的命令行参数来测试它。
 */
//javac -encoding UTF-8 Exercise520.java
//java Exercise520 "这是第一个参数" "这是第二个参数"
public class Exercise520 {
    // 包含可变参数列表的main方法
    public static void main(String... args) {
        System.out.println("测试可变参数的main方法");
        System.out.println("接收到" + args.length + "个命令行参数:");
        
        for(int i = 0; i < args.length; i++) {
            System.out.println("参数 " + i + ": " + args[i]);
        }
        
        // 如果没有参数
        if(args.length == 0) {
            System.out.println("没有接收到任何命令行参数。");
            System.out.println("可以通过以下方式运行并传递参数:");
            System.out.println("java Exercise520 arg1 arg2 arg3 ...");
        }
        
        System.out.println("\n注意事项:");
        System.out.println("1. Java中的main方法参数一直都是可变的，只是通常写作String[] args");
        System.out.println("2. 'String... args' 与 'String[] args' 在功能上是等价的");
        System.out.println("3. 可以通过命令行向程序传递不同数量的参数来测试");
    }
} 