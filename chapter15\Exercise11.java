package chapter15;

import java.util.*;

/**
 * Exercise 11: Test New.java by creating your own classes and
 * ensuring that New will work properly with them.
 */

// New utility class
class New {
    public static <K,V> Map<K,V> map() {
        return new HashMap<K,V>();
    }
    
    public static <T> List<T> list() {
        return new ArrayList<T>();
    }
    
    public static <T> LinkedList<T> lList() {
        return new LinkedList<T>();
    }
    
    public static <T> Set<T> set() {
        return new HashSet<T>();
    }
    
    public static <T> Queue<T> queue() {
        return new LinkedList<T>();
    }
}

// Custom classes for testing
class Person {
    private String name;
    private int age;
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    @Override
    public String toString() {
        return name + " (" + age + ")";
    }
    
    @Override
    public boolean equals(Object o) {
        if(!(o instanceof Person)) return false;
        Person p = (Person)o;
        return name.equals(p.name) && age == p.age;
    }
    
    @Override
    public int hashCode() {
        return name.hashCode() * 37 + age;
    }
}

class Car {
    private String model;
    private String color;
    
    public Car(String model, String color) {
        this.model = model;
        this.color = color;
    }
    
    @Override
    public String toString() {
        return color + " " + model;
    }
}

public class Exercise11 {
    public static void main(String[] args) {
        // Testing with Map
        Map<String, Integer> scoreMap = New.map();
        scoreMap.put("Alice", 95);
        scoreMap.put("Bob", 82);
        scoreMap.put("Charlie", 78);
        System.out.println("Score Map: " + scoreMap);
        
        // Testing with custom class as key
        Map<Person, Car> personCarMap = New.map();
        personCarMap.put(new Person("Alice", 30), new Car("Tesla", "Red"));
        personCarMap.put(new Person("Bob", 25), new Car("Toyota", "Blue"));
        personCarMap.put(new Person("Charlie", 35), new Car("Honda", "White"));
        System.out.println("Person-Car Map: " + personCarMap);
        
        // Testing with List
        List<Person> personList = New.list();
        personList.add(new Person("Alice", 30));
        personList.add(new Person("Bob", 25));
        personList.add(new Person("Charlie", 35));
        System.out.println("Person List: " + personList);
        
        // Testing with LinkedList
        LinkedList<Car> carLinkedList = New.lList();
        carLinkedList.add(new Car("Tesla", "Red"));
        carLinkedList.add(new Car("Toyota", "Blue"));
        carLinkedList.add(new Car("Honda", "White"));
        System.out.println("Car LinkedList: " + carLinkedList);
        
        // Testing with Set
        Set<String> stringSet = New.set();
        stringSet.add("Java");
        stringSet.add("Python");
        stringSet.add("C++");
        stringSet.add("Java"); // Duplicates are ignored in a Set
        System.out.println("String Set: " + stringSet);
        
        // Testing with Queue
        Queue<Integer> intQueue = New.queue();
        intQueue.offer(1);
        intQueue.offer(2);
        intQueue.offer(3);
        System.out.println("Integer Queue: " + intQueue);
        System.out.println("Queue poll: " + intQueue.poll());
        System.out.println("Queue after poll: " + intQueue);
    }
} 