// 练习8：创建一个基类，它仅有一个非默认构造器；再创建一个导出类，
// 它带有默认构造器和非默认构造器。在导出类构造器中调用基类构造器。

package chapter7;

class BaseWithNonDefaultConstructor {
    private int i;
    
    // Only non-default constructor:
    BaseWithNonDefaultConstructor(int i) {
        this.i = i;
        System.out.println("BaseWithNonDefaultConstructor(" + i + ")");
    }
    
    public int getValue() {
        return i;
    }
}

class DerivedClass extends BaseWithNonDefaultConstructor {
    // Default constructor:
    DerivedClass() {
        super(47);
        System.out.println("DerivedClass()");
    }
    
    // Non-default constructor:
    DerivedClass(int i) {
        super(i);
        System.out.println("DerivedClass(" + i + ")");
    }
}

public class Exercise78 {
    public static void main(String[] args) {
        DerivedClass d1 = new DerivedClass();
        DerivedClass d2 = new DerivedClass(73);
        System.out.println("d1.getValue() = " + d1.getValue());
        System.out.println("d2.getValue() = " + d2.getValue());
    }
} 