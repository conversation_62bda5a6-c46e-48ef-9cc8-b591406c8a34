// Exercise 4: Add a method to the class Sequence.SequenceSelector 
// that produces the reference to the outer class Sequence.

interface Selector4 {
    boolean end();
    Object current();
    void next();
}

class Sequence4 {
    private Object[] items;
    private int next = 0;
    
    public Sequence4(int size) {
        items = new Object[size];
    }
    
    public void add(Object x) {
        if(next < items.length)
            items[next++] = x;
    }
    
    private class SequenceSelector implements Selector4 {
        private int i = 0;
        
        @Override
        public boolean end() {
            return i == items.length;
        }
        
        @Override
        public Object current() {
            return items[i];
        }
        
        @Override
        public void next() {
            if(i < items.length) i++;
        }
        
        // Method that returns reference to the outer class:
        public Sequence4 sequence() {
            return Sequence4.this;
        }
    }
    
    public Selector4 selector() {
        return new SequenceSelector();
    }
    
    // Method to test if a selector belongs to this sequence
    public boolean hasSelector(Selector4 s) {
        if(s instanceof SequenceSelector) {
            SequenceSelector ss = (SequenceSelector)s;
            return ss.sequence() == this;
        }
        return false;
    }
}

public class Exercise104 {
    public static void main(String[] args) {
        Sequence4 sequence = new Sequence4(10);
        
        for(int i = 0; i < 10; i++)
            sequence.add(Integer.toString(i));
        
        Selector4 selector = sequence.selector();
        
        // Test that selector belongs to the sequence
        System.out.println("Selector belongs to sequence: " + 
            sequence.hasSelector(selector));
        
        // Use the selector to display all elements
        while(!selector.end()) {
            System.out.print(selector.current() + " ");
            selector.next();
        }
        
        System.out.println("\nExercise 4 completed successfully");
    }
} 