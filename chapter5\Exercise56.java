/**
 * 练习6：修改前一个练习的程序，令每个重载方法都能在调用另一个重载方法时，打印一条消息。
 */
class Dog6 {
    void bark() {
        System.out.println("调用默认bark()方法");
        System.out.println("默认的狗吠声");
    }
    
    void bark(int count) {
        System.out.println("调用bark(int)方法");
        for(int i = 0; i < count; i++)
            bark();
        System.out.println("狗吠了" + count + "次");
    }
    
    void bark(double pitch) {
        System.out.println("调用bark(double)方法");
        if (pitch < 1.0) {
            System.out.println("低沉的狗吠声: 呜呜...");
            bark(true); // 调用bark(boolean)方法
        }
        else if (pitch > 2.0) {
            System.out.println("高声的狗吠声: 汪汪汪!!!");
            bark('A'); // 调用bark(char)方法
        }
        else {
            System.out.println("正常的狗吠声: 汪汪!");
            bark(2); // 调用bark(int)方法
        }
    }
    
    void bark(char mood) {
        System.out.println("调用bark(char)方法");
        switch(mood) {
            case 'A':
                System.out.println("生气的咆哮: 呜呜呜呜呜!!!");
                break;
            case 'H':
                System.out.println("开心的吠叫: 汪汪汪!");
                bark(false); // 调用bark(boolean)方法
                break;
            case 'S':
                System.out.println("悲伤的嚎叫: 嗷呜...");
                break;
            default:
                System.out.println("平静的狗吠声");
                bark(); // 调用默认bark()方法
        }
    }
    
    void bark(boolean hungry) {
        System.out.println("调用bark(boolean)方法");
        if(hungry) {
            System.out.println("饥饿的哀嚎: 呜呜呜...");
            bark('S'); // 调用bark(char)方法
        }
        else {
            System.out.println("满足的低吠: 呼噜呼噜...");
            bark('H'); // 调用bark(char)方法，但注意避免无限递归
        }
    }
}

public class Exercise56 {
    public static void main(String[] args) {
        Dog6 dog = new Dog6();
        System.out.println("测试 bark(double):");
        dog.bark(2.5);
        System.out.println("\n测试 bark(boolean):");
        dog.bark(true);
    }
} 