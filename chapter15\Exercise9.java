package chapter15;

/**
 * Exercise 9: Modify GenericMethods.java so that f() accepts three arguments,
 * all of which are of a different parameterized type.
 */

// Modified GenericMethods class with three different parameterized types
class GenericMethods {
    // Original method with a single generic parameter
    public <T> void f(T x) {
        System.out.println("Original f(): " + x.getClass().getSimpleName());
    }
    
    // Modified method with three different generic parameters
    public <A, B, C> void f(A a, B b, C c) {
        System.out.println("Modified f() with three parameters:");
        System.out.println("  Parameter 1: " + a.getClass().getSimpleName() + " = " + a);
        System.out.println("  Parameter 2: " + b.getClass().getSimpleName() + " = " + b);
        System.out.println("  Parameter 3: " + c.getClass().getSimpleName() + " = " + c);
    }
}

public class Exercise9 {
    public static void main(String[] args) {
        GenericMethods gm = new GenericMethods();
        
        // Test the original method
        gm.f("Hello");
        gm.f(123);
        gm.f(1.0);
        gm.f(1.0f);
        gm.f('c');
        gm.f(gm);
        
        System.out.println();
        
        // Test the modified method with three different types
        gm.f("A string", 123, 1.0);
        gm.f(1.0f, 'c', gm);
        gm.f(new Integer(42), new Double(3.14), new Character('X'));
        
        // Test with same types (still works due to different type parameters)
        gm.f("First", "Second", "Third");
        
        // Test with null values
        System.out.println("\nTesting with null values:");
        try {
            gm.f(null, null, null);
        } catch(Exception e) {
            System.out.println("Exception: " + e);
        }
        
        // Test with mixed nulls
        gm.f("Not null", null, 42);
    }
} 