// Exercise 10: Repeat the previous exercise but define the inner class 
// within a scope within a method.

interface Service10 {
    void doService();
    String getServiceDescription();
}

public class Exercise1010 {
    // Method that defines an inner class within a scope and returns a reference to the interface
    public Service10 getService(final String description) {
        // Create a scope within the method using curly braces
        {
            // Inner class defined inside a scope within a method
            class ServiceImplementation implements Service10 {
                @Override
                public void doService() {
                    System.out.println("Service implementation performing service");
                }
                
                @Override
                public String getServiceDescription() {
                    return description;
                }
            }
            
            // Return from within the scope
            return new ServiceImplementation();
        }
        // The scope ends here, but the reference to the inner class instance remains valid
    }
    
    public static void main(String[] args) {
        Exercise1010 ex = new Exercise1010();
        
        // Get interface reference from method
        Service10 service = ex.getService("Test service");
        
        // Use the interface methods
        System.out.println("Service description: " + service.getServiceDescription());
        service.doService();
        
        System.out.println("\nExercise 10 completed successfully");
    }
} 