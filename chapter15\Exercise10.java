package chapter15;

/**
 * Exercise 10: Modify the previous exercise so that one of f()'s arguments
 * is non-parameterized.
 */

// Modified GenericMethods class with two parameterized types and one non-parameterized
class GenericMethods10 {
    // Original method with a single generic parameter
    public <T> void f(T x) {
        System.out.println("Original f(): " + x.getClass().getSimpleName());
    }
    
    // Modified method with two different generic parameters and one non-parameterized String
    public <A, B> void f(A a, B b, String c) {
        System.out.println("Modified f() with two generic parameters and one String:");
        System.out.println("  Parameter 1 (generic): " + a.getClass().getSimpleName() + " = " + a);
        System.out.println("  Parameter 2 (generic): " + b.getClass().getSimpleName() + " = " + b);
        System.out.println("  Parameter 3 (String): " + c);
    }
    
    // Overloaded method to handle null String
    public <A, B> void f(A a, B b, String c, boolean dummy) {
        System.out.println("Modified f() with two generic parameters and one String (possibly null):");
        System.out.println("  Parameter 1 (generic): " + a.getClass().getSimpleName() + " = " + a);
        System.out.println("  Parameter 2 (generic): " + b.getClass().getSimpleName() + " = " + b);
        System.out.println("  Parameter 3 (String): " + (c == null ? "null" : c));
    }
}

public class Exercise10 {
    public static void main(String[] args) {
        GenericMethods10 gm = new GenericMethods10();
        
        // Test the original method
        gm.f("Hello");
        gm.f(123);
        gm.f(1.0);
        
        System.out.println();
        
        // Test the modified method with two different types and a String
        gm.f(42, 3.14, "This is a String");
        gm.f('X', new Object(), "Another String");
        
        // Test with same types for the generic parameters
        gm.f("First", "Second", "Third");
        
        // Test with null values for generic parameters
        System.out.println("\nTesting with null generic parameters:");
        try {
            // This will cause NullPointerException when trying to call getClass()
            gm.f(null, null, "Not null String");
        } catch(Exception e) {
            System.out.println("Exception: " + e);
        }
        
        // Test with null String (using the overloaded method)
        System.out.println("\nTesting with null String parameter:");
        gm.f(42, 3.14, null, true);
        
        // Demonstrate type inference
        System.out.println("\nDemonstrating type inference:");
        gm.f(new Integer(42), new Double(3.14), "Type inference works");
    }
} 