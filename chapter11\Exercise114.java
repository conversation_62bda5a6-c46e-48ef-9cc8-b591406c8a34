package chapter11;

// 练习4：创建泛型类和方法，演示泛型的使用

public class Exercise114 {
    public static void main(String[] args) {
        // 使用泛型类
        System.out.println("1. 使用泛型类：");
        
        // Integer类型
        Pair<Integer, String> studentScore = new Pair<>(95, "优秀");
        System.out.println("学生成绩: " + studentScore.getFirst() + ", 评价: " + studentScore.getSecond());
        
        // String类型
        Pair<String, String> translation = new Pair<>("Hello", "你好");
        System.out.println("英文: " + translation.getFirst() + ", 中文: " + translation.getSecond());
        
        // 修改值
        studentScore.setFirst(88);
        studentScore.setSecond("良好");
        System.out.println("修改后学生成绩: " + studentScore.getFirst() + ", 评价: " + studentScore.getSecond());
        
        // 使用泛型方法
        System.out.println("\n2. 使用泛型方法：");
        
        // 整数数组
        Integer[] integers = {1, 2, 3, 4, 5};
        System.out.print("打印整数数组: ");
        printArray(integers);
        
        // 字符串数组
        String[] strings = {"Hello", "World", "Java", "Generics"};
        System.out.print("打印字符串数组: ");
        printArray(strings);
        
        // 查找元素
        System.out.println("\n3. 使用泛型方法查找元素：");
        System.out.println("整数3在数组中的位置: " + findElement(integers, 3));
        System.out.println("整数6在数组中的位置: " + findElement(integers, 6));
        System.out.println("字符串'Java'在数组中的位置: " + findElement(strings, "Java"));
        
        // 使用有界泛型
        System.out.println("\n4. 使用有界泛型：");
        
        System.out.println("整数数组最大值: " + findMax(integers));
        
        Double[] doubles = {1.1, 3.3, 2.2, 5.5, 4.4};
        System.out.println("浮点数数组最大值: " + findMax(doubles));
        
        // 泛型与继承
        System.out.println("\n5. 泛型与继承关系：");
        
        Box<Integer> integerBox = new Box<>();
        integerBox.set(123);
        System.out.println("整数盒子: " + integerBox.get());
        
        Box<String> stringBox = new Box<>();
        stringBox.set("Hello Generics");
        System.out.println("字符串盒子: " + stringBox.get());
        
        // 打印盒子内容
        System.out.println("\n6. 使用通配符：");
        printBoxContent(integerBox);
        printBoxContent(stringBox);
    }
    
    // 泛型方法 - 打印任意类型数组
    public static <T> void printArray(T[] array) {
        for (T element : array) {
            System.out.print(element + " ");
        }
        System.out.println();
    }
    
    // 泛型方法 - 查找元素
    public static <T> int findElement(T[] array, T element) {
        for (int i = 0; i < array.length; i++) {
            if (array[i].equals(element)) {
                return i;
            }
        }
        return -1;  // 如果没找到返回-1
    }
    
    // 有界泛型方法 - 找出数组中最大的元素
    public static <T extends Comparable<T>> T findMax(T[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        
        T max = array[0];
        for (int i = 1; i < array.length; i++) {
            if (array[i].compareTo(max) > 0) {
                max = array[i];
            }
        }
        return max;
    }
    
    // 使用通配符的方法
    public static void printBoxContent(Box<?> box) {
        System.out.println("盒子中的内容: " + box.get());
    }
}

// 泛型类 - 包含两个不同类型的元素
class Pair<F, S> {
    private F first;
    private S second;
    
    public Pair(F first, S second) {
        this.first = first;
        this.second = second;
    }
    
    public F getFirst() {
        return first;
    }
    
    public void setFirst(F first) {
        this.first = first;
    }
    
    public S getSecond() {
        return second;
    }
    
    public void setSecond(S second) {
        this.second = second;
    }
}

// 简单泛型类
class Box<T> {
    private T content;
    
    public void set(T content) {
        this.content = content;
    }
    
    public T get() {
        return content;
    }
} 