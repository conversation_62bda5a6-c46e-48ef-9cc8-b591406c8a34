// 练习14：创建一个Car类，其中包含一个Engine、Wheel、Door和Window类型的成员。
// 在Car中创建一个返回Engine引用的方法。在main()中，创建并初始化一个Car对象，
// 然后调用方法获取Engine的引用，并调用Engine的方法，以证明它能正常工作。

package chapter7;

class Engine {
    public void start() {
        System.out.println("Engine started");
    }
    
    public void rev() {
        System.out.println("Engine revving");
    }
    
    public void stop() {
        System.out.println("Engine stopped");
    }
}

class Wheel {
    public void inflate(int psi) {
        System.out.println("Wheel inflated to " + psi + " psi");
    }
}

class Window {
    public void rollup() {
        System.out.println("Window rolled up");
    }
    
    public void rolldown() {
        System.out.println("Window rolled down");
    }
}

class Door {
    private Window window = new Window();
    
    public void open() {
        System.out.println("Door opened");
    }
    
    public void close() {
        System.out.println("Door closed");
    }
    
    public Window getWindow() {
        return window;
    }
}

class Car {
    private Engine engine = new Engine();
    private Wheel[] wheels = new Wheel[4];
    private Door leftDoor = new Door();
    private Door rightDoor = new Door();
    
    public Car() {
        for(int i = 0; i < 4; i++)
            wheels[i] = new Wheel();
    }
    
    public Engine getEngine() {
        return engine;
    }
}

public class Exercise714 {
    public static void main(String[] args) {
        Car car = new Car();
        Engine engine = car.getEngine();
        engine.start();
        engine.rev();
        engine.stop();
    }
} 