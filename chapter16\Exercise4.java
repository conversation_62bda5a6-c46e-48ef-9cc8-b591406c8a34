package chapter16;

import java.util.*;

/**
 * Exercise 4: Repeat Exercise 3 using a three-dimensional array of double.
 */

public class Exercise4 {
    /**
     * Creates and initializes a 3D array of doubles.
     * @param xSize Size in x dimension
     * @param ySize Size in y dimension
     * @param zSize Size in z dimension
     * @param start Starting value for the range
     * @param end Ending value for the range
     * @return The initialized 3D array
     */
    public static double[][][] create3DArray(int xSize, int ySize, int zSize, double start, double end) {
        if (xSize <= 0 || ySize <= 0 || zSize <= 0) {
            throw new IllegalArgumentException("Array dimensions must be positive");
        }
        
        double[][][] result = new double[xSize][ySize][zSize];
        double step = (end - start) / (xSize * ySize * zSize - 1);
        
        for(int i = 0; i < xSize; i++) {
            for(int j = 0; j < ySize; j++) {
                for(int k = 0; k < zSize; k++) {
                    // Calculate position in the flattened array
                    int position = (i * ySize * zSize) + (j * zSize) + k;
                    // Set the value based on position in range
                    result[i][j][k] = start + (step * position);
                }
            }
        }
        
        return result;
    }
    
    // Helper method to print a 3D array with formatting
    public static void print3DArray(double[][][] array) {
        for(int i = 0; i < array.length; i++) {
            System.out.println("Layer " + i + ":");
            for(int j = 0; j < array[i].length; j++) {
                for(int k = 0; k < array[i][j].length; k++) {
                    System.out.printf("%9.4f ", array[i][j][k]);
                }
                System.out.println();
            }
            System.out.println();
        }
    }
    
    public static void main(String[] args) {
        // Test case 1: Small 3D array with integer range
        System.out.println("2x2x2 array with range 1.0 to 8.0:");
        double[][][] array1 = create3DArray(2, 2, 2, 1.0, 8.0);
        print3DArray(array1);
        
        // Test case 2: Rectangular 3D array with decimal range
        System.out.println("2x3x2 array with range 0.0 to 11.0:");
        double[][][] array2 = create3DArray(2, 3, 2, 0.0, 11.0);
        print3DArray(array2);
        
        // Test case 3: 3D array with negative to positive range
        System.out.println("2x2x3 array with range -10.0 to 10.0:");
        double[][][] array3 = create3DArray(2, 2, 3, -10.0, 10.0);
        print3DArray(array3);
        
        // Verify our algorithm by checking first and last elements
        System.out.println("Verification:");
        System.out.println("First element should be start value: " + array1[0][0][0]);
        
        int lastX = array1.length - 1;
        int lastY = array1[lastX].length - 1;
        int lastZ = array1[lastX][lastY].length - 1;
        System.out.println("Last element should be end value: " + array1[lastX][lastY][lastZ]);
    }
} 