package chapter12;

import java.io.*;

/**
 * Exercise 6: Modify the FileOutputShortcut.java example so that it uses a 
 * simple text format to write the data to a file instead of XML.
 */
public class Exercise126 {
    public static void main(String[] args) throws IOException {
        BufferedReader in = new BufferedReader(
            new StringReader(BufferedInputFile.read("chapter12/Exercise126.java"))
        );
        
        // Writing to a plain text file instead of XML
        PrintWriter out = new PrintWriter(
            new BufferedWriter(new FileWriter("chapter12/Exercise126_output.txt"))
        );
        
        int lineCount = 1;
        String s;
        while((s = in.readLine()) != null) {
            // Simple text format: line number followed by the line content
            out.println(lineCount + ": " + s);
            lineCount++;
        }
        out.close();
        
        // Show the stored file
        System.out.println("Stored file contents:");
        System.out.println(BufferedInputFile.read("chapter12/Exercise126_output.txt"));
    }
    
    // Utility class to read file contents
    static class BufferedInputFile {
        public static String read(String filename) throws IOException {
            BufferedReader in = new BufferedReader(new FileReader(filename));
            String s;
            StringBuilder sb = new StringBuilder();
            while((s = in.readLine()) != null)
                sb.append(s + "\n");
            in.close();
            return sb.toString();
        }
    }
} 