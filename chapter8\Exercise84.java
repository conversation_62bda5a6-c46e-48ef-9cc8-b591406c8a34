// 练习4：创建Shape类的一个继承层次：Circle、Square、Triangle等，每个类都覆盖Shape的draw()方法。
// 创建一个包含所有这些形状的数组，遍历该数组并调用各个对象的draw()方法。


class Shape2 {
    public void draw() {
        System.out.println("Shape");
    }
}

class Circle2 extends Shape2 {
    @Override
    public void draw() {
        System.out.println("Circle");
    }
}

class Square extends Shape2 {
    @Override
    public void draw() {
        System.out.println("Square");
    }
}

class Triangle extends Shape2 {
    @Override
    public void draw() {
        System.out.println("Triangle");
    }
}

public class Exercise84 {
    public static void main(String[] args) {
        // Array of shapes
        Shape2[] shapes = {
            new Circle2(),
            new Square(),
            new Triangle()
        };
        
        // Demonstrate polymorphism by calling draw() on each shape
        for(Shape2 shape : shapes) {
            shape.draw();
        }
    }
} 