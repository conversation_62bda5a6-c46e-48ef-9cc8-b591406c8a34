// 练习2：在练习1的基础上，添加方法wheels()返回轮子数量，并在main()中验证。


class Cycle2 {
    public void ride() {
        System.out.println("Cycle riding");
    }
    
    public int wheels() {
        return 0;
    }
}

class Unicycle2 extends Cycle2 {
    @Override
    public void ride() {
        System.out.println("Unicycle riding");
    }
    
    @Override
    public int wheels() {
        return 1;
    }
}

class Bicycle2 extends Cycle2 {
    @Override
    public void ride() {
        System.out.println("Bicycle riding");
    }
    
    @Override
    public int wheels() {
        return 2;
    }
}

class Tricycle2 extends Cycle2 {
    @Override
    public void ride() {
        System.out.println("Tricycle riding");
    }
    
    @Override
    public int wheels() {
        return 3;
    }
}

public class Exercise82 {
    public static void main(String[] args) {
        Cycle2[] cycles = {
            new Unicycle2(),
            new Bicycle2(),
            new Tricycle2()
        };
        
        // Demonstrate polymorphism
        for(Cycle2 c : cycles) {
            c.ride();
            System.out.println("Number of wheels: " + c.wheels());
        }
    }
} 