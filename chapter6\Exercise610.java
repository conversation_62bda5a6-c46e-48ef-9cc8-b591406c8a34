// 练习610：创建一个具有private、protected和public方法的类。创建这个类的对象，看看你能调用哪些方法。

public class Exercise610 {
    public static void main(String[] args) {
        System.out.println("练习610：测试方法访问权限。");
        
        AccessMethods obj = new AccessMethods();
        
        // 可以调用public方法
        obj.publicMethod();
        
        // 可以调用protected方法（在同一包中）
        obj.protectedMethod();
        
        // 可以调用包访问方法（在同一包中）
        obj.packageMethod();
        
        // 不能调用private方法
        //obj.privateMethod(); // 编译错误：不可见
    }
}

class AccessMethods {
    public void publicMethod() {
        System.out.println("公共方法可以从任何地方调用");
    }
    
    protected void protectedMethod() {
        System.out.println("受保护方法可以从同一包或子类调用");
    }
    
    void packageMethod() {
        System.out.println("包访问方法可以从同一包调用");
    }
    
    private void privateMethod() {
        System.out.println("私有方法只能从这个类内部调用");
    }
} 