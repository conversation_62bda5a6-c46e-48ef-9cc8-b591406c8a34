import access.foreign.Foreign;
import access.local.LocalTest;

public class Exercise69 {
    public static void main(String[] args) {
        System.out.println("练习69：理解包访问权限。");
        
        System.out.println("这个练习演示了具有包访问权限（默认访问权限）的类");
        System.out.println("不能从另一个包中访问。");
        
        System.out.println("\n编译器生成错误是因为PackagedClass具有包访问权限（没有访问修饰符），");
        System.out.println("这意味着它只能在自己的包（access.local）内访问。");
        System.out.println("Foreign类在不同的包（access.foreign）中，所以它不能访问PackagedClass。");
        
        System.out.println("\n如果Foreign是access.local包的一部分，那么它就能够");
        System.out.println("访问PackagedClass，因为它们将在同一个包中。");
        
        // 演示
        System.out.println("\n演示：");
        System.out.println("\n1. 不同包中的访问尝试：");
        new Foreign().test();
        
        System.out.println("\n2. 同一包中的访问尝试：");
        new LocalTest().test();
    }
} 