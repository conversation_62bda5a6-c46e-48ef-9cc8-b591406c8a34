package chapter15;

import java.util.*;

/**
 * Exercise 21: Modify ClassTypeCapture.java so that it can also capture
 * and display information about generic types.
 */

// Enhanced ClassTypeCapture with support for generic types
class ClassTypeCapture<T> {
    Class<T> kind;
    Map<String, Class<?>> types = new HashMap<>();
    
    public ClassTypeCapture(Class<T> kind) {
        this.kind = kind;
    }
    
    public boolean f(Object arg) {
        return kind.isInstance(arg);
    }
    
    public void addType(String typeName, Class<?> kind) {
        types.put(typeName, kind);
    }
    
    public Object createNew(String typeName) {
        Class<?> cl = types.get(typeName);
        if(cl == null) {
            System.out.println("Type " + typeName + " not available");
            return null;
        }
        try {
            return cl.newInstance();
        } catch(Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    // Method to display generic type information
    @SuppressWarnings("unchecked")
    public <A> void displayGenericType(A arg) {
        Class<?> actualClass = arg.getClass();
        System.out.println("Actual class: " + actualClass.getSimpleName());
        
        // Display interfaces
        Class<?>[] interfaces = actualClass.getInterfaces();
        System.out.println("Implemented interfaces:");
        for(Class<?> iface : interfaces) {
            System.out.println("  " + iface.getSimpleName());
        }
        
        // Display superclass
        Class<?> superclass = actualClass.getSuperclass();
        System.out.println("Superclass: " + 
            (superclass != null ? superclass.getSimpleName() : "None"));
        
        // Check if object is of the captured type
        if(kind.isAssignableFrom(actualClass)) {
            System.out.println("Object is of the captured type: " + 
                kind.getSimpleName());
        } else {
            System.out.println("Object is NOT of the captured type: " + 
                kind.getSimpleName());
        }
        
        // If the object is a collection, try to examine its elements
        if(arg instanceof Collection) {
            Collection<Object> coll = (Collection<Object>)arg;
            System.out.println("Collection with " + coll.size() + " elements");
            
            if(!coll.isEmpty()) {
                Object firstElement = coll.iterator().next();
                if(firstElement != null) {
                    System.out.println("First element type: " + 
                        firstElement.getClass().getSimpleName());
                }
            }
        }
        
        // If the object is a map, try to examine its keys and values
        if(arg instanceof Map) {
            Map<Object, Object> map = (Map<Object, Object>)arg;
            System.out.println("Map with " + map.size() + " entries");
            
            if(!map.isEmpty()) {
                Map.Entry<Object, Object> firstEntry = 
                    map.entrySet().iterator().next();
                Object key = firstEntry.getKey();
                Object value = firstEntry.getValue();
                
                System.out.println("Key type: " + 
                    (key != null ? key.getClass().getSimpleName() : "null"));
                System.out.println("Value type: " + 
                    (value != null ? value.getClass().getSimpleName() : "null"));
            }
        }
        
        System.out.println();
    }
}

public class Exercise21 {
    public static void main(String[] args) {
        // Capture for String class
        ClassTypeCapture<String> ctt1 = new ClassTypeCapture<>(String.class);
        System.out.println("ctt1.f(\"Hello\"): " + ctt1.f("Hello"));
        System.out.println("ctt1.f(1): " + ctt1.f(1));
        
        // Capture for Number class
        ClassTypeCapture<Number> ctt2 = new ClassTypeCapture<>(Number.class);
        System.out.println("ctt2.f(1): " + ctt2.f(1));
        System.out.println("ctt2.f(1.0): " + ctt2.f(1.0));
        System.out.println("ctt2.f(\"Hello\"): " + ctt2.f("Hello"));
        
        // Test the addType and createNew methods
        ctt1.addType("ArrayList", ArrayList.class);
        ctt1.addType("HashMap", HashMap.class);
        
        ArrayList<String> list = (ArrayList<String>)ctt1.createNew("ArrayList");
        list.add("Hello");
        list.add("World");
        
        HashMap<String, Integer> map = (HashMap<String, Integer>)ctt1.createNew("HashMap");
        map.put("one", 1);
        map.put("two", 2);
        
        // Test displaying generic type information
        System.out.println("\nGeneric type information for String:");
        ctt1.displayGenericType("Hello");
        
        System.out.println("Generic type information for Integer:");
        ctt2.displayGenericType(42);
        
        System.out.println("Generic type information for ArrayList<String>:");
        ctt1.displayGenericType(list);
        
        System.out.println("Generic type information for HashMap<String, Integer>:");
        ctt1.displayGenericType(map);
        
        // Test with a custom generic class
        class Pair<A, B> {
            private A first;
            private B second;
            
            public Pair(A a, B b) {
                first = a;
                second = b;
            }
            
            public A getFirst() { return first; }
            public B getSecond() { return second; }
            
            @Override
            public String toString() {
                return "(" + first + ", " + second + ")";
            }
        }
        
        Pair<String, Integer> pair = new Pair<>("Hello", 42);
        System.out.println("Generic type information for Pair<String, Integer>:");
        ctt1.displayGenericType(pair);
    }
} 