package chapter14;

/**
 * Exercise 2: Create a holder class that holds three objects of the same type,
 * along with the methods to store and retrieve these objects.
 */
public class Exercise2 {
    public static void main(String[] args) {
        // Test with String type
        ThreeHolder<String> stringHolder = new ThreeHolder<>("First", "Second", "Third");
        System.out.println("First: " + stringHolder.getFirst());
        System.out.println("Second: " + stringHolder.getSecond());
        System.out.println("Third: " + stringHolder.getThird());
        
        // Test with Integer type
        ThreeHolder<Integer> intHolder = new ThreeHolder<>(1, 2, 3);
        System.out.println("First: " + intHolder.getFirst());
        System.out.println("Second: " + intHolder.getSecond());
        System.out.println("Third: " + intHolder.getThird());
        
        // Modify values
        intHolder.setFirst(10);
        intHolder.setSecond(20);
        intHolder.setThird(30);
        System.out.println("Modified values:");
        System.out.println("First: " + intHolder.getFirst());
        System.out.println("Second: " + intHolder.getSecond());
        System.out.println("Third: " + intHolder.getThird());
    }
}

class ThreeHolder<T> {
    private T first;
    private T second;
    private T third;
    
    public ThreeHolder(T first, T second, T third) {
        this.first = first;
        this.second = second;
        this.third = third;
    }
    
    public T getFirst() { return first; }
    public T getSecond() { return second; }
    public T getThird() { return third; }
    
    public void setFirst(T first) { this.first = first; }
    public void setSecond(T second) { this.second = second; }
    public void setThird(T third) { this.third = third; }
} 