// Exercise 3: Modify Exercise 1 so that Outer has a private String field 
// (initialized by the constructor), and Inner has a toString() that 
// displays this field. Create an object of type Inner and display it.

class Outer3 {
    private String text;
    
    public Outer3(String text) {
        this.text = text;
    }
    
    class Inner {
        @Override
        public String toString() {
            return text; // Access private field of outer class
        }
    }
    
    public Inner getInner() {
        return new Inner();
    }
}

public class Exercise103 {
    public static void main(String[] args) {
        Outer3 outer = new Outer3("Hello from Outer3");
        Outer3.Inner inner = outer.getInner();
        System.out.println(inner.toString());
        
        // Create another instance with different text
        Outer3 outer2 = new Outer3("Different text");
        Outer3.Inner inner2 = outer2.getInner();
        System.out.println(inner2.toString());
        
        System.out.println("Exercise 3 completed successfully");
    }
} 