// 练习8：在Sandwich.java中创建一个接口，修改fast food层次结构，以使得实现接口的动作成为创建每个类型的三明治的一部分。


interface FastFood {
    void prepare();
    void wrap();
}

class Meal {
    Meal() {
        System.out.println("Meal()");
    }
}

class Bread {
    Bread() {
        System.out.println("Bread()");
    }
}

class Cheese {
    Cheese() {
        System.out.println("Cheese()");
    }
}

class Lettuce {
    Lettuce() {
        System.out.println("Lettuce()");
    }
}

class Lunch extends Meal {
    Lunch() {
        System.out.println("Lunch()");
    }
}

class PortableLunch extends Lunch {
    PortableLunch() {
        System.out.println("PortableLunch()");
    }
}

class Sandwich extends PortableLunch implements FastFood {
    private Bread b = new Bread();
    private Cheese c = new Cheese();
    private Lettuce l = new Lettuce();
    
    public Sandwich() {
        System.out.println("Sandwich()");
    }
    
    @Override
    public void prepare() {
        System.out.println("Making sandwich with bread, cheese, and lettuce");
    }
    
    @Override
    public void wrap() {
        System.out.println("Wrapping sandwich in paper");
    }
}

public class Exercise88 {
    public static void main(String[] args) {
        FastFood food = new Sandwich();
        food.prepare();
        food.wrap();
    }
} 