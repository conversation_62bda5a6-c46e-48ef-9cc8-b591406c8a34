/**
 * 练习19：编写一个类，它接受可变参数的String数组。
 * 验证你可以向该方法传递一个用逗号分隔的String列表，或是一个String[]。
 */
class VarArgs {
    // 接受可变参数的方法
    public void process(String... args) {
        System.out.println("接收到" + args.length + "个参数:");
        for(int i = 0; i < args.length; i++) {
            System.out.println("[" + i + "]: " + args[i]);
        }
        System.out.println();
    }
}

public class Exercise519 {
    public static void main(String[] args) {
        VarArgs va = new VarArgs();
        
        // 传递逗号分隔的参数列表
        System.out.println("传递逗号分隔的参数列表:");
        va.process("一", "二", "三", "四");
        
        // 传递一个String数组
        System.out.println("传递String[]数组:");
        String[] strArray = {"五", "六", "七", "八", "九"};
        va.process(strArray);
        
        // 传递单个参数
        System.out.println("传递单个参数:");
        va.process("单个参数测试");
        
        // 传递空参数列表
        System.out.println("传递空参数列表:");
        va.process();
        
        // 混合传递参数和数组
        System.out.println("混合传递数组和单个参数(在Java中不支持):");
        System.out.println("// 注意：下面的语法在Java中不支持：");
        System.out.println("// va.process(\"前缀\", strArray);");
        System.out.println("// 但可以手动将数组和其他参数结合后传递");
    }
} 