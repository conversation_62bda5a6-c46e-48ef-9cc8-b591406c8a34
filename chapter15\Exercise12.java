package chapter15;

import java.util.*;

/**
 * Exercise 12: Repeat Exercise 11 but use explicit type specification.
 */

// New utility class with explicit type arguments
class NewExplicit {
    public static <K,V> Map<K,V> map() {
        return new HashMap<K,V>();
    }
    
    public static <T> List<T> list() {
        return new ArrayList<T>();
    }
    
    public static <T> LinkedList<T> lList() {
        return new LinkedList<T>();
    }
    
    public static <T> Set<T> set() {
        return new HashSet<T>();
    }
    
    public static <T> Queue<T> queue() {
        return new LinkedList<T>();
    }
}

// Custom classes for testing (same as in Exercise11)
class Person12 {
    private String name;
    private int age;
    
    public Person12(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    @Override
    public String toString() {
        return name + " (" + age + ")";
    }
    
    @Override
    public boolean equals(Object o) {
        if(!(o instanceof Person12)) return false;
        Person12 p = (Person12)o;
        return name.equals(p.name) && age == p.age;
    }
    
    @Override
    public int hashCode() {
        return name.hashCode() * 37 + age;
    }
}

class Car12 {
    private String model;
    private String color;
    
    public Car12(String model, String color) {
        this.model = model;
        this.color = color;
    }
    
    @Override
    public String toString() {
        return color + " " + model;
    }
}

public class Exercise12 {
    public static void main(String[] args) {
        // Testing with Map using explicit type arguments
        Map<String, Integer> scoreMap = NewExplicit.<String, Integer>map();
        scoreMap.put("Alice", 95);
        scoreMap.put("Bob", 82);
        scoreMap.put("Charlie", 78);
        System.out.println("Score Map (explicit): " + scoreMap);
        
        // Testing with custom class as key using explicit type arguments
        Map<Person12, Car12> personCarMap = 
            NewExplicit.<Person12, Car12>map();
        personCarMap.put(new Person12("Alice", 30), new Car12("Tesla", "Red"));
        personCarMap.put(new Person12("Bob", 25), new Car12("Toyota", "Blue"));
        personCarMap.put(new Person12("Charlie", 35), new Car12("Honda", "White"));
        System.out.println("Person-Car Map (explicit): " + personCarMap);
        
        // Testing with List using explicit type arguments
        List<Person12> personList = NewExplicit.<Person12>list();
        personList.add(new Person12("Alice", 30));
        personList.add(new Person12("Bob", 25));
        personList.add(new Person12("Charlie", 35));
        System.out.println("Person List (explicit): " + personList);
        
        // Testing with LinkedList using explicit type arguments
        LinkedList<Car12> carLinkedList = NewExplicit.<Car12>lList();
        carLinkedList.add(new Car12("Tesla", "Red"));
        carLinkedList.add(new Car12("Toyota", "Blue"));
        carLinkedList.add(new Car12("Honda", "White"));
        System.out.println("Car LinkedList (explicit): " + carLinkedList);
        
        // Testing with Set using explicit type arguments
        Set<String> stringSet = NewExplicit.<String>set();
        stringSet.add("Java");
        stringSet.add("Python");
        stringSet.add("C++");
        stringSet.add("Java"); // Duplicates are ignored in a Set
        System.out.println("String Set (explicit): " + stringSet);
        
        // Testing with Queue using explicit type arguments
        Queue<Integer> intQueue = NewExplicit.<Integer>queue();
        intQueue.offer(1);
        intQueue.offer(2);
        intQueue.offer(3);
        System.out.println("Integer Queue (explicit): " + intQueue);
        System.out.println("Queue poll: " + intQueue.poll());
        System.out.println("Queue after poll: " + intQueue);
        
        System.out.println("\nNote: The explicit type specification using '<Type>' " +
            "is not necessary in most cases due to type inference, " +
            "but it can make the code more readable in some situations.");
    }
} 