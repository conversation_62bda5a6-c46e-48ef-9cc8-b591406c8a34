package chapter16;

import java.util.Arrays;
import java.util.Random;

/**
 * Exercise 6: (2) Write a method that takes an array of BerylliumSphere as an
 * argument. Call the method, creating the argument dynamically. Demonstrate that
 * ordinary aggregate array initialization doesn't work in this case. Discover the
 * only situations where ordinary aggregate array initialization works, and where
 * dynamic aggregate initialization works.
 */
public class Exercise6 {
    public static void main(String[] args) {
        // Dynamic aggregate initialization works with method calls:
        testArray(new BerylliumSphere[] { 
            new BerylliumSphere(), 
            new BerylliumSphere(), 
            new BerylliumSphere() 
        });
        
        // This also works (shorter syntax for method argument):
        testArray(new BerylliumSphere[] { 
            new BerylliumSphere(), 
            new BerylliumSphere() 
        });
        
        // This is even shorter - auto-conversion to array for varargs:
        testVarargs(new BerylliumSphere(), new BerylliumSphere());
        
        // This won't compile - ordinary aggregate initialization only works
        // during array declaration, not when passing to a method:
        // testArray({ new BerylliumSphere(), new BerylliumSphere() }); // Error!
        
        // Ordinary aggregate initialization works during declaration:
        BerylliumSphere[] sphereArray = { 
            new BerylliumSphere(), 
            new BerylliumSphere() 
        };
        testArray(sphereArray);
        
        // Dynamic aggregate initialization also works during declaration:
        BerylliumSphere[] dynamicArray = new BerylliumSphere[] {
            new BerylliumSphere(),
            new BerylliumSphere()
        };
        testArray(dynamicArray);
        
        System.out.println("\nConclusions:");
        System.out.println("1. Ordinary aggregate initialization only works during array declaration");
        System.out.println("2. Dynamic aggregate initialization works both during declaration and in method calls");
        System.out.println("3. Varargs provides the most convenient syntax for passing arrays to methods");
    }
    
    public static void testArray(BerylliumSphere[] spheres) {
        System.out.println("\nArray contains " + spheres.length + " BerylliumSphere objects:");
        for(BerylliumSphere sphere : spheres) {
            System.out.println(sphere);
        }
    }
    
    public static void testVarargs(BerylliumSphere... spheres) {
        System.out.println("\nVarargs contains " + spheres.length + " BerylliumSphere objects:");
        for(BerylliumSphere sphere : spheres) {
            System.out.println(sphere);
        }
    }
    
    static class BerylliumSphere {
        private static long counter;
        private final long id = counter++;
        
        @Override
        public String toString() {
            return "Sphere " + id;
        }
    }
} 