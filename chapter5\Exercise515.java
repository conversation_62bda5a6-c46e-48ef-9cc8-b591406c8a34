/**
 * 练习15：创建一个String对象的数组，并对该数组的每一个元素都赋以一个String。
 * 用for循环来打印该数组。
 */
public class Exercise515 {
    public static void main(String[] args) {
        // 创建一个String对象的数组
        String[] stringArray = new String[5];
        
        // 对数组的每个元素赋值
        stringArray[0] = "这是第一个字符串";
        stringArray[1] = "这是第二个字符串";
        stringArray[2] = "这是第三个字符串";
        stringArray[3] = "这是第四个字符串";
        stringArray[4] = "这是第五个字符串";
        
        // 用for循环打印数组
        System.out.println("使用标准for循环打印数组:");
        for(int i = 0; i < stringArray.length; i++) {
            System.out.println("stringArray[" + i + "] = " + stringArray[i]);
        }
        
        // 使用增强for循环
        System.out.println("\n使用增强for循环(for-each)打印数组:");
        int i = 0;
        for(String s : stringArray) {
            System.out.println("stringArray[" + i + "] = " + s);
            i++;
        }
    }
} 