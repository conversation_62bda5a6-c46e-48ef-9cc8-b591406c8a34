// 练习7：创建一个带有构造器的简单类，它接受一个String参数。在构造阶段，
// 打印该参数。创建一个继承自该类的第二个类，它也有一个接受String参数的构造器，
// 并传递给基类的构造器。

package chapter7;

class WithConstructor {
    WithConstructor(String s) {
        System.out.println("WithConstructor constructor: " + s);
    }
}

class SecondClass extends WithConstructor {
    SecondClass(String s) {
        super(s);
        System.out.println("SecondClass constructor: " + s);
    }
}

public class Exercise77 {
    public static void main(String[] args) {
        SecondClass sc = new SecondClass("Passed to constructors");
    }
} 