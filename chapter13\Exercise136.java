package chapter13;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Exercise 13.6: Thread Pools and Callable Tasks
 * 
 * This exercise demonstrates advanced uses of thread pools and callable tasks:
 * 1. Creating different types of thread pools
 * 2. Working with Callable and Future for returned results
 * 3. Using CompletableFuture for asynchronous programming
 * 4. Handling exceptions in asynchronous tasks
 */
public class Exercise136 {
    
    private static final Random random = new Random();
    
    public static void main(String[] args) {
        System.out.println("==== Thread Pools and Callable Tasks ====");
        
        // 1. Different types of thread pools
        differentThreadPoolsExample();
        
        // 2. Callable and Future
        callableAndFutureExample();
        
        // 3. CompletableFuture
        completableFutureExample();
        
        // 4. Exception handling in asynchronous tasks
        exceptionHandlingExample();
        
        System.out.println("\nAll thread pool and callable examples have completed!");
    }
    
    private static void differentThreadPoolsExample() {
        System.out.println("\n1. Different Types of Thread Pools:");
        
        // a) Fixed Thread Pool
        System.out.println("\na) Fixed Thread Pool (3 threads):");
        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(3);
        submitTasksToExecutor(fixedThreadPool, "Fixed-Thread-Pool");
        
        // b) Cached Thread Pool
        System.out.println("\nb) Cached Thread Pool (creates threads as needed):");
        ExecutorService cachedThreadPool = Executors.newCachedThreadPool();
        submitTasksToExecutor(cachedThreadPool, "Cached-Thread-Pool");
        
        // c) Single Thread Executor
        System.out.println("\nc) Single Thread Executor (only 1 thread):");
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        submitTasksToExecutor(singleThreadExecutor, "Single-Thread-Executor");
        
        // d) Scheduled Thread Pool
        System.out.println("\nd) Scheduled Thread Pool (for scheduled tasks):");
        ScheduledExecutorService scheduledThreadPool = Executors.newScheduledThreadPool(2);
        
        // Schedule a task to run after 2 seconds
        scheduledThreadPool.schedule(() -> {
            System.out.println(Thread.currentThread().getName() + ": Running scheduled task after 2 seconds delay");
            return "Completed scheduled task";
        }, 2, TimeUnit.SECONDS);
        
        // Schedule a task to run periodically every 1 second, starting after 1 second
        ScheduledFuture<?> periodicTask = scheduledThreadPool.scheduleAtFixedRate(() -> {
            System.out.println(Thread.currentThread().getName() + ": Running periodic task");
        }, 1, 1, TimeUnit.SECONDS);
        
        // Let the periodic task run for 5 seconds
        try {
            Thread.sleep(5500);
            // Cancel the periodic task
            periodicTask.cancel(false);
            System.out.println("Periodic task cancelled");
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted while waiting for scheduled tasks");
        }
        
        // Shutdown all executors
        fixedThreadPool.shutdown();
        cachedThreadPool.shutdown();
        singleThreadExecutor.shutdown();
        scheduledThreadPool.shutdown();
        
        // Wait for all executors to terminate
        try {
            fixedThreadPool.awaitTermination(5, TimeUnit.SECONDS);
            cachedThreadPool.awaitTermination(5, TimeUnit.SECONDS);
            singleThreadExecutor.awaitTermination(5, TimeUnit.SECONDS);
            scheduledThreadPool.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Interrupted while waiting for executors to terminate");
        }
    }
    
    private static void submitTasksToExecutor(ExecutorService executor, String poolName) {
        // Submit 5 tasks to the executor
        for (int i = 0; i < 5; i++) {
            final int taskId = i;
            executor.submit(() -> {
                String threadName = Thread.currentThread().getName();
                System.out.println(poolName + " - Task " + taskId + " executed by thread: " + threadName);
                try {
                    // Simulate work with random duration
                    Thread.sleep(100 + random.nextInt(400));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return null;
            });
        }
        
        // Wait for tasks to complete
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void callableAndFutureExample() {
        System.out.println("\n2. Callable and Future Example:");
        
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        // Create a list to hold futures
        List<Future<Integer>> futures = new ArrayList<>();
        
        // Submit 5 tasks that compute factorial
        for (int i = 1; i <= 5; i++) {
            final int number = i + 5; // Calculate factorial of 6, 7, 8, 9, 10
            
            // Submit a Callable task that returns a result
            Callable<Integer> factorialTask = () -> {
                System.out.println(Thread.currentThread().getName() + ": Calculating factorial of " + number);
                return calculateFactorial(number);
            };
            
            futures.add(executor.submit(factorialTask));
        }
        
        // Retrieve and print results
        for (int i = 0; i < futures.size(); i++) {
            try {
                int number = i + 6; // 6, 7, 8, 9, 10
                int factorial = futures.get(i).get(); // Blocks until the result is available
                System.out.println("Factorial of " + number + " is: " + factorial);
            } catch (InterruptedException | ExecutionException e) {
                System.out.println("Error getting factorial result: " + e.getMessage());
            }
        }
        
        // Invoice All
        System.out.println("\nUsing invokeAll for batch processing:");
        List<Callable<String>> callables = IntStream.range(1, 6)
            .mapToObj(i -> (Callable<String>) () -> {
                Thread.sleep(random.nextInt(500));
                return "Result-" + i + " from " + Thread.currentThread().getName();
            })
            .collect(Collectors.toList());
        
        try {
            List<Future<String>> batchResults = executor.invokeAll(callables);
            for (int i = 0; i < batchResults.size(); i++) {
                try {
                    System.out.println("Batch result " + (i+1) + ": " + batchResults.get(i).get());
                } catch (ExecutionException e) {
                    System.out.println("Error in batch task " + (i+1) + ": " + e.getMessage());
                }
            }
        } catch (InterruptedException e) {
            System.out.println("Batch processing interrupted: " + e.getMessage());
        }
        
        // Shutdown the executor
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Interrupted while waiting for executor to terminate");
        }
    }
    
    private static void completableFutureExample() {
        System.out.println("\n3. CompletableFuture Example:");
        
        // Create a CompletableFuture that computes a value
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
            System.out.println(Thread.currentThread().getName() + ": Producing a value asynchronously");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Hello";
        });
        
        // Chain operations with thenApply (like map)
        CompletableFuture<String> future2 = future1.thenApply(result -> {
            System.out.println(Thread.currentThread().getName() + ": Transforming the result");
            return result + " World";
        });
        
        // Chain with thenAccept (like forEach, doesn't return a value)
        future2.thenAccept(result -> {
            System.out.println(Thread.currentThread().getName() + ": Final result: " + result);
        });
        
        // Combining multiple CompletableFutures
        CompletableFuture<String> future3 = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Future 3";
        });
        
        CompletableFuture<String> future4 = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(800);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "Future 4";
        });
        
        // Combine results when both are complete
        CompletableFuture<String> combinedFuture = future3.thenCombine(future4, (result1, result2) -> {
            return result1 + " + " + result2;
        });
        
        // Wait for the combinedFuture to complete and get its result
        try {
            String combinedResult = combinedFuture.get();
            System.out.println("Combined result: " + combinedResult);
        } catch (InterruptedException | ExecutionException e) {
            System.out.println("Error getting combined result: " + e.getMessage());
        }
        
        // CompletableFuture with explicit Executor
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        CompletableFuture<String> futureWithExecutor = CompletableFuture.supplyAsync(() -> {
            System.out.println(Thread.currentThread().getName() + ": Running with custom executor");
            return "Custom Executor Result";
        }, executor);
        
        futureWithExecutor.thenAccept(result -> {
            System.out.println(Thread.currentThread().getName() + ": Result from custom executor: " + result);
        });
        
        // Shutdown the executor
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Interrupted while waiting for executor to terminate");
        }
        
        // Wait for all CompletableFuture operations to finish
        try {
            // Add a small delay to ensure CompletableFuture callbacks execute
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void exceptionHandlingExample() {
        System.out.println("\n4. Exception Handling in Asynchronous Tasks:");
        
        // a) Exception in Callable with Future
        System.out.println("\na) Exception in Callable with Future:");
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        Future<Integer> failingFuture = executor.submit(() -> {
            System.out.println("Callable task that will fail");
            throw new IllegalStateException("Deliberate exception in Callable");
        });
        
        try {
            Integer result = failingFuture.get();
            System.out.println("Result: " + result); // This line won't execute
        } catch (ExecutionException e) {
            System.out.println("ExecutionException caught: " + e.getCause().getMessage());
        } catch (InterruptedException e) {
            System.out.println("Task was interrupted");
        }
        
        // b) Exception handling in CompletableFuture
        System.out.println("\nb) Exception handling in CompletableFuture:");
        
        CompletableFuture<String> failingCompletableFuture = CompletableFuture.supplyAsync(() -> {
            System.out.println("CompletableFuture task that will fail");
            if (true) {
                throw new RuntimeException("Deliberate exception in CompletableFuture");
            }
            return "This will not be returned";
        });
        
        // Handle the exception with exceptionally
        CompletableFuture<String> recoveredFuture = failingCompletableFuture.exceptionally(ex -> {
            System.out.println("Recovered from exception: " + ex.getMessage());
            return "Recovered Value";
        });
        
        try {
            String result = recoveredFuture.get();
            System.out.println("Result after recovery: " + result);
        } catch (InterruptedException | ExecutionException e) {
            System.out.println("This shouldn't happen with exceptionally: " + e.getMessage());
        }
        
        // c) Using handle for both success and failure cases
        System.out.println("\nc) Using handle for both success and failure:");
        
        CompletableFuture<String> successfulFuture = CompletableFuture.supplyAsync(() -> "Successful result");
        
        CompletableFuture<String> handledSuccess = successfulFuture.handle((result, ex) -> {
            if (ex != null) {
                return "Error handled: " + ex.getMessage();
            } else {
                return "Success handled: " + result;
            }
        });
        
        CompletableFuture<String> failingFuture2 = CompletableFuture.supplyAsync(() -> {
            throw new RuntimeException("Another deliberate exception");
        });
        
        CompletableFuture<String> handledFailure = failingFuture2.handle((result, ex) -> {
            if (ex != null) {
                return "Error handled: " + ex.getMessage();
            } else {
                return "Success handled: " + result;
            }
        });
        
        try {
            System.out.println(handledSuccess.get());
            System.out.println(handledFailure.get());
        } catch (InterruptedException | ExecutionException e) {
            System.out.println("This shouldn't happen with handle: " + e.getMessage());
        }
        
        // Shutdown the executor
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Interrupted while waiting for executor to terminate");
        }
    }
    
    /**
     * Helper method to calculate factorial
     */
    private static int calculateFactorial(int n) {
        int result = 1;
        for (int i = 2; i <= n; i++) {
            result *= i;
            
            // Simulate some work
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        return result;
    }
}