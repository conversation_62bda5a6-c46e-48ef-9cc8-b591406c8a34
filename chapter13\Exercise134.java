package chapter13;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.Random;
import java.util.ArrayList;
import java.util.List;

/**
 * Exercise 13.4: High-Level Concurrency Utilities
 * 
 * This exercise demonstrates high-level concurrency utilities from java.util.concurrent:
 * 1. Executors and thread pools
 * 2. Atomic variables for thread-safe operations
 * 3. Locks for complex synchronization
 * 4. Concurrent collections
 * 5. CountDownLatch, CyclicBarrier, and Semaphore
 */
public class Exercise134 {
    
    public static void main(String[] args) {
        System.out.println("==== High-Level Concurrency Utilities ====");
        
        // 1. Executor Service and Thread Pools
        executorServiceExample();
        
        // 2. Atomic Variables
        atomicVariablesExample();
        
        // 3. Locks
        lockExample();
        
        // 4. Concurrent Collections
        concurrentCollectionsExample();
        
        // 5. Synchronizers
        synchronizersExample();
        
        System.out.println("\nAll high-level concurrency examples have completed!");
    }
    
    private static void executorServiceExample() {
        System.out.println("\n1. Executor Service and Thread Pools:");
        
        // Fixed thread pool with 3 threads
        System.out.println("\na) Fixed Thread Pool:");
        ExecutorService fixedPool = Executors.newFixedThreadPool(3);
        
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            fixedPool.execute(() -> {
                String threadName = Thread.currentThread().getName();
                System.out.println("Task " + taskId + " executed by " + threadName);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // Using Callable and Future for return values
        System.out.println("\nb) Callable and Future:");
        ExecutorService callablePool = Executors.newFixedThreadPool(2);
        
        List<Future<Integer>> futures = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            futures.add(callablePool.submit(() -> {
                System.out.println("Calculating result for task " + taskId);
                Thread.sleep(1000);
                return taskId * 10;
            }));
        }
        
        // Get results from futures
        for (int i = 0; i < futures.size(); i++) {
            try {
                Integer result = futures.get(i).get();
                System.out.println("Result of task " + (i+1) + ": " + result);
            } catch (InterruptedException | ExecutionException e) {
                System.out.println("Error getting result: " + e.getMessage());
            }
        }
        
        // Scheduled Executor Service
        System.out.println("\nc) Scheduled Executor Service:");
        ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(1);
        
        scheduledPool.schedule(() -> 
            System.out.println("Delayed task executed after 2 seconds"),
            2, TimeUnit.SECONDS);
        
        // Shutdown the executor services
        fixedPool.shutdown();
        callablePool.shutdown();
        scheduledPool.shutdown();
        
        try {
            fixedPool.awaitTermination(5, TimeUnit.SECONDS);
            callablePool.awaitTermination(5, TimeUnit.SECONDS);
            scheduledPool.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Executor service termination interrupted");
        }
    }
    
    private static void atomicVariablesExample() {
        System.out.println("\n2. Atomic Variables:");
        
        // Using AtomicInteger for thread-safe counters
        AtomicInteger atomicCounter = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        for (int i = 0; i < 3; i++) {
            executor.execute(() -> {
                for (int j = 0; j < 1000; j++) {
                    atomicCounter.incrementAndGet();
                }
            });
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Executor termination interrupted");
        }
        
        System.out.println("Atomic counter final value: " + atomicCounter.get() + " (expected: 3000)");
        
        // Demonstrating other atomic operations
        AtomicInteger value = new AtomicInteger(100);
        System.out.println("Initial value: " + value.get());
        
        // Add and get
        System.out.println("After addAndGet(50): " + value.addAndGet(50));
        
        // Compare and set
        boolean updated = value.compareAndSet(150, 200);
        System.out.println("compareAndSet(150, 200) succeeded: " + updated);
        System.out.println("New value: " + value.get());
        
        // Get and update
        int oldValue = value.getAndUpdate(v -> v * 2);
        System.out.println("Old value from getAndUpdate: " + oldValue);
        System.out.println("New value after update: " + value.get());
    }
    
    private static void lockExample() {
        System.out.println("\n3. Locks:");
        
        // ReentrantLock example
        System.out.println("\na) ReentrantLock:");
        final Lock lock = new ReentrantLock();
        final int[] counter = new int[1]; // Using array for mutable integer
        
        ExecutorService lockExecutor = Executors.newFixedThreadPool(2);
        
        for (int i = 0; i < 2; i++) {
            lockExecutor.execute(() -> {
                for (int j = 0; j < 1000; j++) {
                    // Acquire the lock
                    lock.lock();
                    try {
                        counter[0]++;
                    } finally {
                        // Always release the lock in a finally block
                        lock.unlock();
                    }
                }
            });
        }
        
        lockExecutor.shutdown();
        try {
            lockExecutor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Lock executor termination interrupted");
        }
        
        System.out.println("Counter with ReentrantLock: " + counter[0] + " (expected: 2000)");
        
        // ReentrantReadWriteLock example
        System.out.println("\nb) ReentrantReadWriteLock:");
        final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
        final ReentrantReadWriteLock.ReadLock readLock = rwLock.readLock();
        final ReentrantReadWriteLock.WriteLock writeLock = rwLock.writeLock();
        
        final StringBuilder sharedData = new StringBuilder("Initial data");
        
        ExecutorService rwExecutor = Executors.newFixedThreadPool(5);
        
        // Reader threads
        for (int i = 0; i < 3; i++) {
            final int readerId = i + 1;
            rwExecutor.execute(() -> {
                for (int j = 0; j < 3; j++) {
                    readLock.lock();
                    try {
                        System.out.println("Reader " + readerId + " reads: " + sharedData);
                        Thread.sleep(100); // Simulate reading time
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        readLock.unlock();
                    }
                }
            });
        }
        
        // Writer threads
        for (int i = 0; i < 2; i++) {
            final int writerId = i + 1;
            rwExecutor.execute(() -> {
                for (int j = 0; j < 2; j++) {
                    writeLock.lock();
                    try {
                        sharedData.append(" - Updated by Writer " + writerId);
                        System.out.println("Writer " + writerId + " updated data");
                        Thread.sleep(200); // Simulate writing time
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        writeLock.unlock();
                    }
                }
            });
        }
        
        rwExecutor.shutdown();
        try {
            rwExecutor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("RW executor termination interrupted");
        }
        
        System.out.println("Final shared data: " + sharedData);
    }
    
    private static void concurrentCollectionsExample() {
        System.out.println("\n4. Concurrent Collections:");
        
        // ConcurrentHashMap
        System.out.println("\na) ConcurrentHashMap:");
        ConcurrentHashMap<String, Integer> concurrentMap = new ConcurrentHashMap<>();
        
        ExecutorService mapExecutor = Executors.newFixedThreadPool(3);
        
        for (int i = 0; i < 3; i++) {
            final int threadId = i;
            mapExecutor.execute(() -> {
                for (int j = 1; j <= 3; j++) {
                    String key = "Thread" + threadId + "-Key" + j;
                    concurrentMap.put(key, j * 10);
                    
                    // No ConcurrentModificationException even when iterating while modifying
                    concurrentMap.forEach((k, v) -> 
                        System.out.println(Thread.currentThread().getName() + " sees: " + k + " -> " + v));
                    
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
        }
        
        mapExecutor.shutdown();
        try {
            mapExecutor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Map executor termination interrupted");
        }
        
        System.out.println("Final ConcurrentHashMap size: " + concurrentMap.size());
        
        // ConcurrentLinkedQueue
        System.out.println("\nb) ConcurrentLinkedQueue:");
        ConcurrentLinkedQueue<Integer> concurrentQueue = new ConcurrentLinkedQueue<>();
        
        ExecutorService queueExecutor = Executors.newFixedThreadPool(4);
        
        // Producer threads
        for (int i = 0; i < 2; i++) {
            final int producerId = i;
            queueExecutor.execute(() -> {
                for (int j = 1; j <= 5; j++) {
                    int value = (producerId * 10) + j;
                    concurrentQueue.add(value);
                    System.out.println("Producer " + producerId + " added: " + value);
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
        }
        
        // Consumer threads
        for (int i = 0; i < 2; i++) {
            final int consumerId = i;
            queueExecutor.execute(() -> {
                for (int j = 1; j <= 5; j++) {
                    try {
                        Thread.sleep(200); // Consumers are slower
                        Integer value = concurrentQueue.poll();
                        if (value != null) {
                            System.out.println("Consumer " + consumerId + " removed: " + value);
                        } else {
                            System.out.println("Consumer " + consumerId + " found empty queue");
                            j--; // Try again
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
        }
        
        queueExecutor.shutdown();
        try {
            queueExecutor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Queue executor termination interrupted");
        }
        
        System.out.println("Final ConcurrentLinkedQueue size: " + concurrentQueue.size());
    }
    
    private static void synchronizersExample() {
        System.out.println("\n5. Synchronizers:");
        
        // CountDownLatch
        System.out.println("\na) CountDownLatch:");
        CountDownLatch startSignal = new CountDownLatch(1);
        CountDownLatch doneSignal = new CountDownLatch(3);
        
        ExecutorService latchExecutor = Executors.newFixedThreadPool(3);
        
        for (int i = 0; i < 3; i++) {
            final int workerId = i;
            latchExecutor.execute(() -> {
                try {
                    System.out.println("Worker " + workerId + " ready and waiting for start signal");
                    startSignal.await(); // Wait for the start signal
                    
                    System.out.println("Worker " + workerId + " running task");
                    Thread.sleep(1000 + new Random().nextInt(1000)); // Simulate work
                    
                    System.out.println("Worker " + workerId + " completed task");
                    doneSignal.countDown(); // Signal that this worker is done
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        try {
            // Let workers get ready
            Thread.sleep(500);
            System.out.println("All workers ready, sending start signal");
            startSignal.countDown(); // Start all workers
            
            // Wait for all workers to finish
            System.out.println("Main thread waiting for workers to complete");
            doneSignal.await();
            System.out.println("All workers have completed their tasks");
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        // CyclicBarrier
        System.out.println("\nb) CyclicBarrier:");
        final int PARTIES = 3;
        CyclicBarrier barrier = new CyclicBarrier(PARTIES, 
            () -> System.out.println("Barrier action executed - all threads have reached the barrier!"));
        
        ExecutorService barrierExecutor = Executors.newFixedThreadPool(PARTIES);
        
        for (int i = 0; i < PARTIES; i++) {
            final int threadId = i;
            barrierExecutor.execute(() -> {
                try {
                    for (int phase = 1; phase <= 2; phase++) {
                        System.out.println("Thread " + threadId + " working on phase " + phase);
                        Thread.sleep(1000 + threadId * 300); // Simulate work
                        
                        System.out.println("Thread " + threadId + " waiting at barrier for phase " + phase);
                        barrier.await(); // Wait for all threads to reach this point
                        
                        System.out.println("Thread " + threadId + " continuing after barrier for phase " + phase);
                    }
                } catch (InterruptedException | BrokenBarrierException e) {
                    System.out.println("Thread " + threadId + " interrupted or barrier broken");
                }
            });
        }
        
        // Semaphore
        System.out.println("\nc) Semaphore:");
        // Semaphore with 2 permits - only 2 threads can access resource at once
        Semaphore semaphore = new Semaphore(2);
        
        ExecutorService semaphoreExecutor = Executors.newFixedThreadPool(5);
        
        for (int i = 0; i < 5; i++) {
            final int userId = i;
            semaphoreExecutor.execute(() -> {
                try {
                    System.out.println("User " + userId + " waiting to access the resource");
                    semaphore.acquire(); // Acquire a permit
                    
                    System.out.println("User " + userId + " accessing the resource");
                    Thread.sleep(1000); // Simulate resource usage
                    
                    System.out.println("User " + userId + " releasing the resource");
                    semaphore.release(); // Release the permit
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // Shutdown executors
        latchExecutor.shutdown();
        barrierExecutor.shutdown();
        semaphoreExecutor.shutdown();
        
        try {
            latchExecutor.awaitTermination(10, TimeUnit.SECONDS);
            barrierExecutor.awaitTermination(10, TimeUnit.SECONDS);
            semaphoreExecutor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            System.out.println("Synchronizers example interrupted during shutdown");
        }
    }
}