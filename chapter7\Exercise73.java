// 练习3：证明基类构造器总是会被调用，且在导出类构造器之前被调用。

package chapter7;

class Base {
    private int i;
    
    Base(int i) {
        this.i = i;
        System.out.println("Base constructor, i = " + i);
    }
    
    public void set(int i) {
        this.i = i;
    }
    
    public int get() {
        return i;
    }
}

public class Exercise73 extends Base {
    public Exercise73(int i) {
        super(i);
        System.out.println("Exercise73 constructor, i = " + i);
    }
    
    public static void main(String[] args) {
        Exercise73 e = new Exercise73(47);
        e.set(99);
        System.out.println("e.get() = " + e.get());
    }
} 