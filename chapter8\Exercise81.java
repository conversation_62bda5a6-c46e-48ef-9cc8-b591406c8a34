// 练习1：创建一个Cycle类，有子类Unicycle、Bicycle和Tricycle。演示每个类型的实例可以向上转型为Cycle。


class Cycle {
    public void ride() {
        System.out.println("Cycle riding");
    }
}

class Unicycle extends Cycle {
    @Override
    public void ride() {
        System.out.println("Unicycle riding");
    }
}

class Bicycle extends Cycle {
    @Override
    public void ride() {
        System.out.println("Bicycle riding");
    }
}

class Tricycle extends Cycle {
    @Override
    public void ride() {
        System.out.println("Tricycle riding");
    }
}

public class Exercise81 {
    public static void main(String[] args) {
        Cycle[] cycles = {
            new Unicycle(),
            new Bicycle(),
            new Tricycle()
        };
        
        // Demonstrate upcasting
        for(Cycle c : cycles) {
            c.ride();
        }
    }
} 