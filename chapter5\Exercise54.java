/**
 * 练习4：为前一个练习中的类添加一个重载构造器，令其接受一个字符串参数，
 * 并在构造器中把你自己的信息和接受的参数一起打印出来。
 */
class Test4 {
    Test4() {
        System.out.println("这是默认构造器（无参构造器）中的消息");
    }
    
    Test4(String s) {
        System.out.println("这是带参数的构造器，参数是: " + s);
        System.out.println("我的信息: Java编程爱好者");
    }
}

public class Exercise54 {
    public static void main(String[] args) {
        new Test4(); // 调用无参构造器
        new Test4("Hello, Java!"); // 调用带参数的构造器
    }
} 