// Exercise 4: Create a private inner class that implements a public interface.
// Write a method that returns a reference to an instance of the private inner
// class, upcast to the interface. Show that the inner class is completely
// hidden by trying to downcast to it.
// (This is actually the same as Exercise 2, but it's a good exercise to do twice.)

interface Selector {
    boolean end();
    Object current();
    void next();
}

public class Exercise94 {
    private Object[] items;
    private int next = 0;
    
    public Exercise94(int size) {
        items = new Object[size];
    }
    
    public void add(Object x) {
        if(next < items.length)
            items[next++] = x;
    }
    
    private class SequenceSelector implements Selector {
        private int i = 0;
        
        @Override
        public boolean end() {
            return i == items.length;
        }
        
        @Override
        public Object current() {
            return items[i];
        }
        
        @Override
        public void next() {
            if(i < items.length) i++;
        }
    }
    
    public Selector selector() {
        return new SequenceSelector();
    }
    
    public static void main(String[] args) {
        Exercise94 sequence = new Exercise94(10);
        for(int i = 0; i < 10; i++)
            sequence.add(Integer.toString(i));
        
        Selector selector = sequence.selector();
        while(!selector.end()) {
            System.out.print(selector.current() + " ");
            selector.next();
        }
        System.out.println();
        
        // Can't do this - SequenceSelector is not visible:
        // SequenceSelector ss = (SequenceSelector)selector;
        
        System.out.println("Exercise 4 completed successfully");
    }
} 