/**
 * 练习21：创建一个枚举类型，它包含纸币中最常见的6种面值。
 * 通过values()循环并打印每一个值及其ordinal()。
 */
public class Exercise521 {
    // 定义枚举类型，包含6种常见纸币面值
    enum Currency {
        ONE(1),        // 1元
        FIVE(5),       // 5元
        TEN(10),       // 10元
        TWENTY(20),    // 20元
        FIFTY(50),     // 50元
        HUNDRED(100);  // 100元
        
        private int value;
        
        // 枚举构造器，指定每种面值的数值
        Currency(int value) {
            this.value = value;
        }
        
        // 获取面值
        public int getValue() {
            return value;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("枚举类型示例 - 纸币面值");
        
        // 通过values()获取所有枚举值并循环打印
        System.out.println("\n货币面值列表:");
        System.out.println("名称\t\t序号(ordinal)\t面值");
        System.out.println("------------------------------------");
        
        // 遍历Currency枚举的所有值
        for(Currency c : Currency.values()) {
            System.out.println(c + "\t\t" + c.ordinal() + "\t\t" + c.getValue() + "元");
        }
        
        System.out.println("\n补充说明:");
        System.out.println("1. ordinal()返回枚举常量在枚举声明中的位置，从0开始计数");
        System.out.println("2. values()返回包含所有枚举值的数组");
        System.out.println("3. 枚举类型可以有构造器、字段和方法");
    }
} 