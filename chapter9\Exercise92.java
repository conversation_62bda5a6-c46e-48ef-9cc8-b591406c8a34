// Exercise 2: Create a private inner class that implements a public interface. 
// Write a method that returns a reference to an instance of the private inner 
// class, upcast to the interface. Show that the inner class is completely 
// hidden by trying to downcast to it.

interface Destination {
    String readLabel();
}

public class Exercise92 {
    private class PDestination implements Destination {
        private String label;
        
        private PDestination(String whereTo) {
            label = whereTo;
        }
        
        @Override
        public String readLabel() {
            return label;
        }
    }
    
    public Destination getDestination(String s) {
        return new PDestination(s);
    }
    
    public static void main(String[] args) {
        Exercise92 p = new Exercise92();
        Destination d = p.getDestination("Tasmania");
        System.out.println(d.readLabel());
        
        // Can't do this - compiler will complain that PDestination 
        // is not a visible type:
        // PDestination pd = (PDestination)d;
        
        System.out.println("Exercise 2 completed successfully");
    }
} 