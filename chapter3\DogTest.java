import static net.mindview.util.Print.print;
//E5
class Dog2 {
	String name;
	String spark;
	void setName(String n) {
		name = n;
	}
	void setSpark(String s) {
		spark = s;
	}
	void showName() {
		print(name);
	}
	void speak() {
		print(spark);
	}
}

public class DogTest {
	public static void main(String[] args) {
		Dog2 spot = new Dog2();
		spot.setName("DD");
		spot.setSpark("Hi");
		Dog2 scruffy = new Dog2();
		scruffy.setName("MM");
		scruffy.setSpark("Hello");
		spot.showName();
		spot.speak();
		scruffy.showName(); 
		scruffy.speak();
	}
}