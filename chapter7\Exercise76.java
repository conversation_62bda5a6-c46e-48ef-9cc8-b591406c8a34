// 练习6：使用Chess.java，证明如果你不为导出类创建构造器，
// 编译器会为你合成一个默认构造器，该构造器将调用基类构造器。

package chapter7;

class Game {
    Game(int i) {
        System.out.println("Game constructor with int " + i);
    }
}

class BoardGame extends Game {
    BoardGame(int i) {
        super(i);
        System.out.println("BoardGame constructor with int " + i);
    }
}

class Chess extends BoardGame {
    // No constructor defined
    Chess() {
        super(11);
        System.out.println("Chess constructor");
    }
}

public class Exercise76 {
    public static void main(String[] args) {
        Chess x = new Chess();
    }
} 