// Exercise 5: Create a class with an inner class. In a separate class,
// make an instance of the inner class.

class Outer5 {
    private int value = 5;
    
    class Inner {
        private int innerValue = 10;
        
        public void display() {
            System.out.println("Outer value: " + value);
            System.out.println("Inner value: " + innerValue);
        }
    }
}

// Separate class that creates an instance of the inner class
class InnerClassUser {
    public static void useInner() {
        // To create an inner class instance, we need an instance of the outer class first
        Outer5 outer = new Outer5();
        
        // Then we can create the inner class instance
        Outer5.Inner inner = outer.new Inner();
        inner.display();
    }
}

public class Exercise105 {
    public static void main(String[] args) {
        // Call the method in the separate class that creates and uses the inner class
        InnerClassUser.useInner();
        
        // We can also create the inner class instance directly here
        Outer5 outer = new Outer5();
        Outer5.Inner inner = outer.new Inner();
        inner.display();
        
        System.out.println("Exercise 5 completed successfully");
    }
} 