package chapter12;

// 练习1：文件基本操作，创建、删除、检查文件和目录

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Exercise121 {
    public static void main(String[] args) {
        // 创建文件对象
        File file = new File("chapter12/test_file.txt");
        File dir = new File("chapter12/test_dir");
        
        try {
            // 1. 文件创建
            System.out.println("1. 文件创建：");
            if (!file.exists()) {
                boolean created = file.createNewFile();
                System.out.println("文件创建" + (created ? "成功" : "失败") + ": " + file.getAbsolutePath());
            } else {
                System.out.println("文件已经存在: " + file.getAbsolutePath());
            }
            
            // 2. 目录创建
            System.out.println("\n2. 目录创建：");
            if (!dir.exists()) {
                boolean created = dir.mkdir();
                System.out.println("目录创建" + (created ? "成功" : "失败") + ": " + dir.getAbsolutePath());
            } else {
                System.out.println("目录已经存在: " + dir.getAbsolutePath());
            }
            
            // 3. 创建多级目录
            System.out.println("\n3. 多级目录创建：");
            File multiDir = new File("chapter12/test_dir/sub1/sub2");
            boolean created = multiDir.mkdirs();
            System.out.println("多级目录创建" + (created ? "成功" : "失败") + ": " + multiDir.getAbsolutePath());
            
            // 4. 文件信息获取
            System.out.println("\n4. 文件信息：");
            System.out.println("文件名称: " + file.getName());
            System.out.println("文件路径: " + file.getPath());
            System.out.println("绝对路径: " + file.getAbsolutePath());
            System.out.println("规范路径: " + file.getCanonicalPath());
            System.out.println("父目录: " + file.getParent());
            System.out.println("是否存在: " + file.exists());
            System.out.println("是否为文件: " + file.isFile());
            System.out.println("是否为目录: " + file.isDirectory());
            System.out.println("是否可读: " + file.canRead());
            System.out.println("是否可写: " + file.canWrite());
            System.out.println("是否可执行: " + file.canExecute());
            System.out.println("文件大小: " + file.length() + " 字节");
            
            // 格式化最后修改时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("最后修改时间: " + sdf.format(new Date(file.lastModified())));
            
            // 5. 文件重命名/移动
            System.out.println("\n5. 文件重命名/移动：");
            File renamedFile = new File("chapter12/test_file_renamed.txt");
            boolean renamed = file.renameTo(renamedFile);
            System.out.println("文件重命名" + (renamed ? "成功" : "失败") + 
                               ": " + file.getName() + " -> " + renamedFile.getName());
            
            // 如果重命名成功，使用重命名后的文件对象进行后续操作
            if (renamed) {
                file = renamedFile;
            }
            
            // 6. 列出目录内容
            System.out.println("\n6. 列出目录内容：");
            File chapterDir = new File("chapter12");
            File[] files = chapterDir.listFiles();
            
            if (files != null) {
                System.out.println("chapter12目录中的文件和子目录：");
                for (File f : files) {
                    System.out.println(f.getName() + (f.isDirectory() ? " [目录]" : " [文件]"));
                }
            }
            
            // 7. 筛选文件
            System.out.println("\n7. 使用FilenameFilter筛选文件：");
            String[] txtFiles = chapterDir.list((dir1, name) -> name.endsWith(".txt"));
            
            if (txtFiles != null && txtFiles.length > 0) {
                System.out.println("以.txt结尾的文件：");
                for (String name : txtFiles) {
                    System.out.println(name);
                }
            } else {
                System.out.println("没有找到.txt文件");
            }
            
            // 8. 删除文件
            System.out.println("\n8. 删除文件：");
            boolean deleted = file.delete();
            System.out.println("文件删除" + (deleted ? "成功" : "失败") + ": " + file.getName());
            
            // 9. 删除目录（目录必须为空）
            System.out.println("\n9. 删除空目录：");
            File emptyDir = new File("chapter12/empty_dir");
            emptyDir.mkdir();
            deleted = emptyDir.delete();
            System.out.println("空目录删除" + (deleted ? "成功" : "失败") + ": " + emptyDir.getName());
            
            // 10. 递归删除非空目录
            System.out.println("\n10. 递归删除非空目录：");
            System.out.println("递归删除目录: " + dir.getAbsolutePath());
            boolean success = deleteDirectory(dir);
            System.out.println("非空目录删除" + (success ? "成功" : "失败"));
            
        } catch (IOException e) {
            System.out.println("发生I/O异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 递归删除目录及其内容
    private static boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                        System.out.println("删除文件: " + file.getAbsolutePath());
                    }
                }
            }
        }
        boolean result = directory.delete();
        if (result) {
            System.out.println("删除目录: " + directory.getAbsolutePath());
        }
        return result;
    }
} 