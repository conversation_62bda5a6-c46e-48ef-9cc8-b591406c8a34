// 练习6：在练习1的基础上，添加Cycle的一个方法，它用的是wheels()方法。在Unicycle、Bicycle和Tricycle中覆盖wheels()，
// 返回不同的值。创建各个类的实例，向上转型为Cycle，然后调用方法，看看会发生什么。


class Cycle4 {
    public void ride() {
        System.out.println("Cycle riding");
    }
    
    public int wheels() {
        return 0;
    }
    
    public void describe() {
        System.out.println("This vehicle has " + wheels() + " wheels");
    }
}

class Unicycle4 extends Cycle4 {
    @Override
    public void ride() {
        System.out.println("Unicycle riding");
    }
    
    @Override
    public int wheels() {
        return 1;
    }
}

class Bicycle4 extends Cycle4 {
    @Override
    public void ride() {
        System.out.println("Bicycle riding");
    }
    
    @Override
    public int wheels() {
        return 2;
    }
}

class Tricycle4 extends Cycle4 {
    @Override
    public void ride() {
        System.out.println("Tricycle riding");
    }
    
    @Override
    public int wheels() {
        return 3;
    }
}

public class Exercise86 {
    public static void main(String[] args) {
        Cycle4[] cycles = {
            new Unicycle4(),
            new Bicycle4(),
            new Tricycle4()
        };
        
        // 演示动态绑定
        for(Cycle4 c : cycles) {
            c.ride();
            c.describe(); // 这里将使用被覆盖的wheels()方法
        }
    }
} 