package chapter15;

import java.util.Iterator;

/**
 * Exercise 7: Use composition instead of inheritance to adapt Fibonacci
 * to make it Iterable.
 */

// Original Fibonacci generator
class Fibonacci {
    private int count = 0;
    
    public int next() {
        return fib(count++);
    }
    
    private int fib(int n) {
        if(n < 2) return 1;
        return fib(n-2) + fib(n-1);
    }
}

// Adapter using composition to make Fibonacci Iterable
class IterableFibonacci implements Iterable<Integer> {
    private Fibonacci fib = new Fibonacci();
    private int n;
    
    public IterableFibonacci(int count) {
        n = count;
    }
    
    @Override
    public Iterator<Integer> iterator() {
        return new Iterator<Integer>() {
            private int count = n;
            
            @Override
            public boolean hasNext() {
                return count > 0;
            }
            
            @Override
            public Integer next() {
                count--;
                return fib.next();
            }
            
            @Override
            public void remove() {
                throw new UnsupportedOperationException();
            }
        };
    }
}

public class Exercise7 {
    public static void main(String[] args) {
        // Using the IterableFibonacci adapter
        System.out.println("First 18 Fibonacci numbers:");
        for(int i : new IterableFibonacci(18)) {
            System.out.print(i + " ");
        }
        System.out.println();
        
        // Using the original Fibonacci directly
        System.out.println("\nUsing original Fibonacci directly:");
        Fibonacci fib = new Fibonacci();
        for(int i = 0; i < 18; i++) {
            System.out.print(fib.next() + " ");
        }
        System.out.println();
        
        // Demonstrating that we can create multiple iterators
        System.out.println("\nCreating multiple iterators:");
        IterableFibonacci ifib = new IterableFibonacci(10);
        System.out.println("Iterator 1:");
        for(int i : ifib) {
            System.out.print(i + " ");
        }
        System.out.println("\nIterator 2:");
        for(int i : ifib) {
            System.out.print(i + " ");
        }
        System.out.println();
    }
}