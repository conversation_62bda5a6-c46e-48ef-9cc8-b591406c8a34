// Exercise 5: Create an interface containing three methods, in a package.
// Implement the interface in a different package.

// Note: For simplicity, we're implementing this in a single file,
// but conceptually these would be in separate packages.

// This would be in package interfaces
interface ThreeMethods {
    void firstMethod();
    void secondMethod();
    void thirdMethod();
}

// This would be in a different package
class ThreeMethodsImpl implements ThreeMethods {
    @Override
    public void firstMethod() {
        System.out.println("First method implementation");
    }
    
    @Override
    public void secondMethod() {
        System.out.println("Second method implementation");
    }
    
    @Override
    public void thirdMethod() {
        System.out.println("Third method implementation");
    }
}

public class Exercise95 {
    public static void main(String[] args) {
        ThreeMethods tm = new ThreeMethodsImpl();
        tm.firstMethod();
        tm.secondMethod();
        tm.thirdMethod();
        
        System.out.println("Exercise 5 completed successfully");
    }
} 