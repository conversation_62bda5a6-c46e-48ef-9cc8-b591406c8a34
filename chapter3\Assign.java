import static net.mindview.util.Print.*;
//E2
class Tube {
	float level;
}

public class Assign {
	public static void main(String[] args) {
		Tube t1 = new Tube();
		Tube t2 = new Tube();
		t1.level = 0.1f;
		t2.level = 0.2f;
		print("1: t1.level: " + t1.level + ", t2.level: " + t2.level);
		t1 = t2;
		print("2: t1.level: " + t1.level + ", t2.level: " + t2.level);
		t1.level = 0.3f;
		print("3: t1.level: " + t1.level + ", t2.level: " + t2.level);
	}
}