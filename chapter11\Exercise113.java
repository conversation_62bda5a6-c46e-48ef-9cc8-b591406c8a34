package chapter11;

// 练习3：使用HashMap实现键值对存储，并演示常用操作

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class Exercise113 {
    public static void main(String[] args) {
        // 创建HashMap
        HashMap<String, Integer> studentScores = new HashMap<>();
        
        // 添加键值对
        System.out.println("1. 添加键值对到HashMap：");
        studentScores.put("张三", 95);
        studentScores.put("李四", 88);
        studentScores.put("王五", 76);
        studentScores.put("赵六", 92);
        System.out.println("学生分数映射: " + studentScores);
        
        // 修改键值对
        System.out.println("\n2. 修改键值对：");
        studentScores.put("王五", 82); // 覆盖原有的值
        System.out.println("修改后的映射: " + studentScores);
        
        // 获取值
        System.out.println("\n3. 获取值：");
        System.out.println("张三的分数: " + studentScores.get("张三"));
        System.out.println("不存在的学生分数: " + studentScores.get("陈七"));
        
        // 使用getOrDefault方法
        System.out.println("使用getOrDefault获取陈七的分数: " + 
                           studentScores.getOrDefault("陈七", 0));
        
        // 检查键或值是否存在
        System.out.println("\n4. 检查键或值是否存在：");
        System.out.println("是否包含键'李四': " + studentScores.containsKey("李四"));
        System.out.println("是否包含值92: " + studentScores.containsValue(92));
        
        // 删除键值对
        System.out.println("\n5. 删除键值对：");
        studentScores.remove("赵六");
        System.out.println("删除后的映射: " + studentScores);
        
        // 遍历键值对
        System.out.println("\n6. 遍历键值对：");
        for (Map.Entry<String, Integer> entry : studentScores.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        
        // 获取所有键
        System.out.println("\n7. 获取所有键：");
        Set<String> keys = studentScores.keySet();
        System.out.println("所有键: " + keys);
        
        // 遍历键获取值
        System.out.println("\n8. 通过键遍历获取值：");
        for (String key : studentScores.keySet()) {
            System.out.println(key + ": " + studentScores.get(key));
        }
        
        // 获取所有值
        System.out.println("\n9. 获取所有值：");
        System.out.println("所有值: " + studentScores.values());
        
        // 检查是否为空和大小
        System.out.println("\n10. 检查Map是否为空和大小：");
        System.out.println("Map是否为空: " + studentScores.isEmpty());
        System.out.println("Map大小: " + studentScores.size());
        
        // 合并两个Map
        System.out.println("\n11. 合并两个Map：");
        HashMap<String, Integer> moreStudents = new HashMap<>();
        moreStudents.put("陈七", 85);
        moreStudents.put("张三", 98); // 键冲突会覆盖原有值
        System.out.println("另一个Map: " + moreStudents);
        
        studentScores.putAll(moreStudents);
        System.out.println("合并后的Map: " + studentScores);
        
        // 清空Map
        System.out.println("\n12. 清空Map：");
        studentScores.clear();
        System.out.println("清空后的Map: " + studentScores);
        System.out.println("Map是否为空: " + studentScores.isEmpty());
    }
} 