public class StringCompare {
	public static void main(String[] args) {
		boolean method1 = method1("1", "1");
		System.out.println(method1);
		boolean method2 = method2("1", "1");
		System.out.println(method2);
		boolean method3 = method1("aa", "bb");
		System.out.println(method3);
		boolean method4 = method2("aa", "bb");
		System.out.println(method4);
	}
	static boolean method1(String a, String b){
		return a == b;
	}
	static boolean method2(String a, String b){
		return a.equals(b);
	}
} 