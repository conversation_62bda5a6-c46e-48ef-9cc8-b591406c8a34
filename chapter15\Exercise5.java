package chapter15;

/**
 * Exercise 5: Create a generic class with a method that takes an argument
 * of the generic type. Inside this method, try to call methods of the parameter.
 * Note what methods you can call. Repeat for a generic type parameter that is
 * bounded by a class and/or interfaces.
 */

// Unbounded generic class
class Unbounded<T> {
    private T item;
    
    public Unbounded(T item) {
        this.item = item;
    }
    
    public void examineUnbounded(T item) {
        System.out.println("Examining unbounded type: " + item.getClass().getSimpleName());
        
        // With an unbounded type, we can only call methods from Object
        System.out.println("toString(): " + item.toString());
        System.out.println("hashCode(): " + item.hashCode());
        System.out.println("equals(item): " + item.equals(this.item));
        
        // Cannot call specific methods of the actual type without casting
        // For example, if T is String, we can't directly call length()
        // item.length(); // Won't compile
    }
}

// Interface for bounded type example
interface Sized {
    int size();
}

// Class implementing the interface
class SizedItem implements Sized {
    private int size;
    
    public SizedItem(int size) {
        this.size = size;
    }
    
    @Override
    public int size() {
        return size;
    }
    
    public void doSomething() {
        System.out.println("Doing something with a SizedItem");
    }
}

// Bounded generic class
class Bounded<T extends Sized> {
    private T item;
    
    public Bounded(T item) {
        this.item = item;
    }
    
    public void examineBounded(T item) {
        System.out.println("Examining bounded type: " + item.getClass().getSimpleName());
        
        // With a bounded type, we can call methods from Object
        System.out.println("toString(): " + item.toString());
        System.out.println("hashCode(): " + item.hashCode());
        System.out.println("equals(item): " + item.equals(this.item));
        
        // And also methods from the bounding type (Sized interface)
        System.out.println("size(): " + item.size());
        
        // But still can't call specific methods of the actual type without casting
        // For example, if T is SizedItem, we can't directly call doSomething()
        // item.doSomething(); // Won't compile
        
        // We need to cast:
        if(item instanceof SizedItem) {
            ((SizedItem)item).doSomething();
        }
    }
}

public class Exercise5 {
    public static void main(String[] args) {
        // Test unbounded generic
        String s = "Hello";
        Unbounded<String> u = new Unbounded<>(s);
        u.examineUnbounded(s);
        System.out.println();
        
        // Test bounded generic
        SizedItem sizedItem = new SizedItem(42);
        Bounded<SizedItem> b = new Bounded<>(sizedItem);
        b.examineBounded(sizedItem);
        
        System.out.println("\nConclusion:");
        System.out.println("1. With unbounded generics, you can only call methods from Object.");
        System.out.println("2. With bounded generics, you can call methods from Object and the bounding type.");
        System.out.println("3. To call methods specific to the actual type, you need to cast.");
    }
} 