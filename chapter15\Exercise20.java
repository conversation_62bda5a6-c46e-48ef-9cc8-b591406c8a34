package chapter15;

/**
 * Exercise 20: Create an interface with two methods, and a class that
 * implements that interface and adds another method. In a second class,
 * inherit from the first class and add yet another method. Now create a
 * generic method with an argument type that is bounded by the interface,
 * and show that the various methods are available depending on the
 * actual argument type you pass to the generic method.
 */

// Interface with two methods
interface Interesting {
    void interesting1();
    void interesting2();
}

// First class that implements the interface and adds a method
class MoreInteresting implements Interesting {
    @Override
    public void interesting1() {
        System.out.println("MoreInteresting.interesting1()");
    }
    
    @Override
    public void interesting2() {
        System.out.println("MoreInteresting.interesting2()");
    }
    
    public void interesting3() {
        System.out.println("MoreInteresting.interesting3()");
    }
}

// Second class that inherits from the first and adds another method
class EvenMoreInteresting extends MoreInteresting {
    @Override
    public void interesting1() {
        System.out.println("EvenMoreInteresting.interesting1()");
    }
    
    @Override
    public void interesting2() {
        System.out.println("EvenMoreInteresting.interesting2()");
    }
    
    @Override
    public void interesting3() {
        System.out.println("EvenMoreInteresting.interesting3()");
    }
    
    public void interesting4() {
        System.out.println("EvenMoreInteresting.interesting4()");
    }
}

public class Exercise20 {
    // Generic method with an argument type bounded by the interface
    public static <T extends Interesting> void testInteresting(T item) {
        System.out.println("Testing methods available from " + 
            item.getClass().getSimpleName() + ":");
        
        // These methods are always available because they're in the interface
        item.interesting1();
        item.interesting2();
        
        // This method is not in the interface, need to check and cast
        if(item instanceof MoreInteresting) {
            System.out.println("Item is also MoreInteresting!");
            MoreInteresting moreItem = (MoreInteresting)item;
            moreItem.interesting3();
        }
        
        // This method is only in the second class
        if(item instanceof EvenMoreInteresting) {
            System.out.println("Item is also EvenMoreInteresting!");
            EvenMoreInteresting evenMoreItem = (EvenMoreInteresting)item;
            evenMoreItem.interesting4();
        }
        
        System.out.println();
    }
    
    public static void main(String[] args) {
        // Test with different types that implement Interesting
        MoreInteresting moreInteresting = new MoreInteresting();
        EvenMoreInteresting evenMoreInteresting = new EvenMoreInteresting();
        
        // Both can be passed to the generic method
        testInteresting(moreInteresting);
        testInteresting(evenMoreInteresting);
        
        // Create anonymous class that implements Interesting
        Interesting justInteresting = new Interesting() {
            @Override
            public void interesting1() {
                System.out.println("Anonymous.interesting1()");
            }
            
            @Override
            public void interesting2() {
                System.out.println("Anonymous.interesting2()");
            }
        };
        
        // This works too because it implements the interface
        testInteresting(justInteresting);
        
        System.out.println("Conclusion:");
        System.out.println("1. Generic methods can use bounds to ensure minimum interface requirements");
        System.out.println("2. The actual runtime type determines what methods are available");
        System.out.println("3. Casting is necessary to access methods not in the bound interface");
    }
} 