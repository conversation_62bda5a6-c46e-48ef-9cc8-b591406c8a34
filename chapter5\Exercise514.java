/**
 * 练习14：创建一个类，拥有两个静态字符串域，其中一个在定义处初始化，
 * 另一个在静态块中初始化。现在，加入一个显示这两个字段值的静态方法。
 * 证明静态块中的初始化发生在于其他初始化。
 */
class StaticOrder {
    // 在定义处初始化的静态字段
    static String field1 = initField1();
    
    // 将在静态块中初始化的静态字段
    static String field2;
    
    // 用于跟踪初始化顺序的静态变量
    static boolean field1Initialized = false;
    static boolean field2Initialized = false;
    
    // 初始化字段1的方法
    static String initField1() {
        System.out.println("正在初始化field1");
        field1Initialized = true;
        return "Field 1";
    }
    
    // 静态块，初始化field2
    static {
        System.out.println("静态块开始执行");
        System.out.println("此时field1的初始化状态: " + field1Initialized);
        System.out.println("此时field2的初始化状态: " + field2Initialized);
        
        System.out.println("正在静态块中初始化field2");
        field2 = "Field 2";
        field2Initialized = true;
        
        System.out.println("静态块执行完毕");
    }
    
    // 显示两个字段值的静态方法
    static void displayFields() {
        System.out.println("field1 = " + field1);
        System.out.println("field2 = " + field2);
        System.out.println("field1Initialized = " + field1Initialized);
        System.out.println("field2Initialized = " + field2Initialized);
    }
}

public class Exercise514 {
    public static void main(String[] args) {
        System.out.println("main方法开始执行");
        
        // 调用显示字段的静态方法
        System.out.println("\n调用displayFields()方法:");
        StaticOrder.displayFields();
        
        System.out.println("\n测试结论:");
        System.out.println("1. 静态字段按照它们在类中定义的顺序进行初始化");
        System.out.println("2. 在这个例子中，field1先初始化，然后静态块执行，field2再初始化");
        System.out.println("3. 静态初始化只发生一次，在类被加载时");
        System.out.println("4. 当静态块执行时，先声明的静态字段(field1)已经被初始化");
        System.out.println("5. 如果静态块位于静态字段声明之前，访问未初始化的静态字段将得到默认值");
    }
} 