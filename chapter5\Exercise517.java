/**
 * 练习17：创建一个类，它包含一个String对象的数组，通过构造器初始化该数组。
 */
class StringContainer {
    private String[] strings;
    
    // 构造器，接收String数组并初始化内部数组
    public StringContainer(String[] strArray) {
        strings = strArray;
    }
    
    // 构造器，接收一个整数n并创建包含n个元素的String数组
    public StringContainer(int size) {
        strings = new String[size];
        // 初始化数组元素
        for(int i = 0; i < size; i++) {
            strings[i] = "String " + i;
        }
    }
    
    // 打印数组内容的方法
    public void printArray() {
        for(int i = 0; i < strings.length; i++) {
            System.out.println("strings[" + i + "] = " + strings[i]);
        }
    }
}

public class Exercise517 {
    public static void main(String[] args) {
        // 创建一个String数组
        String[] myStrings = {"苹果", "橙子", "香蕉", "葡萄"};
        
        // 使用已有的String数组创建StringContainer对象
        System.out.println("使用已有数组初始化:");
        StringContainer container1 = new StringContainer(myStrings);
        container1.printArray();
        
        // 使用整数构造器创建StringContainer对象
        System.out.println("\n使用整数参数初始化:");
        StringContainer container2 = new StringContainer(5);
        container2.printArray();
    }
} 