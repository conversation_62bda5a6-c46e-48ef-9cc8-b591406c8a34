package chapter13;

/**
 * Exercise 13.2: Thread Synchronization
 * 
 * This exercise demonstrates various thread synchronization mechanisms:
 * 1. Synchronized methods
 * 2. Synchronized blocks
 * 3. The volatile keyword
 * 4. Thread-safe operations
 */
public class Exercise132 {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("==== Thread Synchronization ====");
        
        // 1. Problem: Race condition without synchronization
        System.out.println("\n1. Race condition without synchronization:");
        Counter unsafeCounter = new Counter();
        
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                unsafeCounter.increment();
            }
        }, "Thread-1");
        
        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                unsafeCounter.increment();
            }
        }, "Thread-2");
        
        t1.start();
        t2.start();
        
        t1.join();
        t2.join();
        
        System.out.println("Unsafe counter final value: " + unsafeCounter.getCount());
        System.out.println("Expected value: 2000");
        
        // 2. Solution: Synchronized method
        System.out.println("\n2. Using synchronized methods:");
        SynchronizedCounter safeCounter = new SynchronizedCounter();
        
        Thread t3 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                safeCounter.increment();
            }
        }, "Thread-3");
        
        Thread t4 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                safeCounter.increment();
            }
        }, "Thread-4");
        
        t3.start();
        t4.start();
        
        t3.join();
        t4.join();
        
        System.out.println("Safe counter (synchronized method) final value: " + safeCounter.getCount());
        System.out.println("Expected value: 2000");
        
        // 3. Synchronized block
        System.out.println("\n3. Using synchronized blocks:");
        BlockSynchronizedCounter blockCounter = new BlockSynchronizedCounter();
        
        Thread t5 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                blockCounter.increment();
            }
        }, "Thread-5");
        
        Thread t6 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                blockCounter.increment();
            }
        }, "Thread-6");
        
        t5.start();
        t6.start();
        
        t5.join();
        t6.join();
        
        System.out.println("Block synchronized counter final value: " + blockCounter.getCount());
        System.out.println("Expected value: 2000");
        
        // 4. Volatile keyword example
        System.out.println("\n4. Volatile keyword example:");
        VolatileExample volatileExample = new VolatileExample();
        
        Thread writerThread = new Thread(() -> {
            try {
                System.out.println("Writer thread starting...");
                Thread.sleep(1000);
                volatileExample.setReady(true);
                System.out.println("Writer thread set ready = true");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "Writer-Thread");
        
        Thread readerThread = new Thread(() -> {
            System.out.println("Reader thread starting...");
            while (!volatileExample.isReady()) {
                // This empty loop will be optimized away without volatile
                // Creating a memory barrier with Thread.yield()
                Thread.yield();
            }
            System.out.println("Reader thread detected ready = true");
        }, "Reader-Thread");
        
        writerThread.start();
        readerThread.start();
        
        writerThread.join();
        readerThread.join();
        
        // 5. Static synchronized method
        System.out.println("\n5. Static synchronized methods:");
        
        Thread t7 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                StaticSynchronizedCounter.increment();
            }
        }, "Thread-7");
        
        Thread t8 = new Thread(() -> {
            for (int i = 0; i < 1000; i++) {
                StaticSynchronizedCounter.increment();
            }
        }, "Thread-8");
        
        t7.start();
        t8.start();
        
        t7.join();
        t8.join();
        
        System.out.println("Static synchronized counter value: " + StaticSynchronizedCounter.getCount());
        System.out.println("Expected value: 2000");
        
        System.out.println("\nAll synchronization examples have completed!");
    }
}

// Unsafe counter without synchronization
class Counter {
    private int count = 0;
    
    public void increment() {
        count++;
    }
    
    public int getCount() {
        return count;
    }
}

// Safe counter with synchronized methods
class SynchronizedCounter {
    private int count = 0;
    
    public synchronized void increment() {
        count++;
    }
    
    public synchronized int getCount() {
        return count;
    }
}

// Counter with synchronized blocks
class BlockSynchronizedCounter {
    private int count = 0;
    private final Object lock = new Object(); // Object used as lock
    
    public void increment() {
        synchronized (lock) {
            count++;
        }
    }
    
    public int getCount() {
        synchronized (lock) {
            return count;
        }
    }
}

// Volatile example for visibility guarantee
class VolatileExample {
    private volatile boolean ready = false;
    
    public boolean isReady() {
        return ready;
    }
    
    public void setReady(boolean ready) {
        this.ready = ready;
    }
}

// Static synchronized counter
class StaticSynchronizedCounter {
    private static int count = 0;
    
    public static synchronized void increment() {
        count++;
    }
    
    public static synchronized int getCount() {
        return count;
    }
} 