package chapter15;

import java.util.*;

/**
 * Exercise 14: Modify BasicGeneratorDemo.java to use the explicit form
 * of creation for the Generator (that is, use the explicit constructor
 * instead of the generic create() method).
 */

// Generator interface
interface Generator14<T> {
    T next();
}

// Basic Generator implementation
class BasicGenerator<T> implements Generator14<T> {
    private Class<T> type;
    
    // Original create() method (static factory method)
    public static <T> Generator14<T> create(Class<T> type) {
        return new BasicGenerator<T>(type);
    }
    
    // Constructor to be used directly for explicit form
    public BasicGenerator(Class<T> type) {
        this.type = type;
    }
    
    @Override
    public T next() {
        try {
            // Assumes type has a default (no-arg) constructor:
            return type.newInstance();
        } catch(Exception e) {
            throw new RuntimeException(e);
        }
    }
}

// Class to test the generator with
class CountedObject {
    private static long counter = 0;
    private final long id = counter++;
    
    public long id() { return id; }
    
    @Override
    public String toString() { 
        return "CountedObject " + id;
    }
}

public class Exercise14 {
    public static void main(String[] args) {
        // Original approach using the create() method
        Generator14<CountedObject> gen1 = BasicGenerator.create(CountedObject.class);
        
        System.out.println("Using create() method:");
        for(int i = 0; i < 5; i++) {
            System.out.println(gen1.next());
        }
        
        // Explicit form using the constructor directly
        Generator14<CountedObject> gen2 = new BasicGenerator<>(CountedObject.class);
        
        System.out.println("\nUsing explicit constructor:");
        for(int i = 0; i < 5; i++) {
            System.out.println(gen2.next());
        }
        
        // Fill a List using the explicit generator
        List<CountedObject> list = new ArrayList<>();
        Generator14<CountedObject> gen3 = new BasicGenerator<>(CountedObject.class);
        
        for(int i = 0; i < 5; i++) {
            list.add(gen3.next());
        }
        
        System.out.println("\nList filled with explicit generator:");
        for(CountedObject co : list) {
            System.out.println(co);
        }
        
        System.out.println("\nComparing approaches:");
        System.out.println("1. Factory method: Generator14<T> gen = BasicGenerator.create(Class<T>)");
        System.out.println("2. Explicit constructor: Generator14<T> gen = new BasicGenerator<T>(Class<T>)");
        System.out.println("\nThe explicit form is slightly more verbose but doesn't hide the constructor call.");
    }
} 