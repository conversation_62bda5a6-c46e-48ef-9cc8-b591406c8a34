// Exercise 6: Create an interface with at least one method, and implement 
// that interface by defining an inner class within a method, which returns 
// a reference to your interface.

interface SimpleInterface {
    void performAction();
    String getDescription();
}

public class Exercise106 {
    // Method that contains an inner class and returns a reference to the interface
    public SimpleInterface getInterfaceReference(final String description) {
        // Inner class defined within a method
        class SimpleInterfaceImpl implements SimpleInterface {
            @Override
            public void performAction() {
                System.out.println("Performing action from inner class defined in method");
            }
            
            @Override
            public String getDescription() {
                return description;
            }
        }
        
        // Return reference to the interface
        return new SimpleInterfaceImpl();
    }
    
    public static void main(String[] args) {
        Exercise106 ex = new Exercise106();
        
        // Get interface reference from method
        SimpleInterface si = ex.getInterfaceReference("First implementation");
        
        // Use the interface methods
        System.out.println("Description: " + si.getDescription());
        si.performAction();
        
        // Get another reference with different description
        SimpleInterface si2 = ex.getInterfaceReference("Second implementation");
        System.out.println("Description: " + si2.getDescription());
        si2.performAction();
        
        System.out.println("Exercise 6 completed successfully");
    }
} 