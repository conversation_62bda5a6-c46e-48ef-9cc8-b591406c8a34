// Exercise 1: Write a class named Outer that contains an inner class named Inner.
// Add a method to Outer that returns an object of type Inner. 
// In main(), create and initialize a reference to an Inner.

class Outer1 {
    class Inner {
        public void display() {
            System.out.println("Inner class method called");
        }
    }
    
    public Inner getInner() {
        return new Inner();
    }
}

public class Exercise101 {
    public static void main(String[] args) {
        Outer1 outer = new Outer1();
        Outer1.Inner inner = outer.getInner();
        inner.display();
        
        // Another way to create an Inner instance:
        Outer1.Inner inner2 = outer.new Inner();
        inner2.display();
        
        System.out.println("Exercise 1 completed successfully");
    }
} 