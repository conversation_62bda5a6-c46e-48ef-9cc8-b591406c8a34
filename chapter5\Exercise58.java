/**
 * 练习8：编写具有两个方法的类，在第一个方法内调用第二个方法两次：
 * 第一次调用时不使用this关键字，第二次调用时使用this关键字——
 * 这里只是为了验证你已经知道怎样使用this，以及在大多数时候这是可选的。
 */
class ThisTest {
    void firstMethod() {
        System.out.println("在firstMethod()中...");
        
        // 第一次调用secondMethod()时不使用this关键字
        System.out.println("不使用this关键字调用secondMethod():");
        secondMethod();
        
        // 第二次调用secondMethod()时使用this关键字
        System.out.println("使用this关键字调用secondMethod():");
        this.secondMethod();
    }
    
    void secondMethod() {
        System.out.println("这是secondMethod()");
    }
}

public class Exercise58 {
    public static void main(String[] args) {
        ThisTest tt = new ThisTest();
        tt.firstMethod();
        
        //System.out.println("\n结论：在大多数情况下，使用this关键字调用当前对象的方法是可选的。");
        //System.out.println("当方法名称没有歧义时，编译器会自动假定是当前对象的方法。");
    }
} 