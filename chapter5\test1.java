import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class test1 {
    public static void main(String[] args) {
        List<Integer> list = new ArrayList<Integer>();
        for (int i = 0; i < 10; i++) {
            list.add(i);
        }
        System.out.println(list);
        int size = 10;
        Iterator<Integer> iterator = list.iterator();
        List<Integer> res = new ArrayList<>();
        while(iterator.hasNext()){
            Integer next = iterator.next();
            if(iterator.hasNext()){
                iterator.next();
            }
            res.add(next);
            iterator.remove();
        }
        System.out.println(res);
    }
}
