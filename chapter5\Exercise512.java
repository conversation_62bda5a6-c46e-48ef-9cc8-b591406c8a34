/**
 * 练习12：编写名为Tank的类（坦克类），此类具有值为满True或空False的状态，
 * 以及改变这个状态的方法。另外，它还有一个检查被回收前状态的finalize()方法。
 * 在main()中测试，确保finalize()方法能在Tank对象被回收前检查Tank是否为空。
 */
class Tank {
    private boolean full = false; // 初始状态为空
    private static int counter = 0;
    private final int id = counter++;
    
    public Tank() {
        System.out.println("Tank " + id + " 被创建，初始状态：空");
    }
    
    // 加满坦克
    public void fill() {
        full = true;
        System.out.println("Tank " + id + " 已加满");
    }
    
    // 清空坦克
    public void empty() {
        full = false;
        System.out.println("Tank " + id + " 已清空");
    }
    
    // 获取当前状态
    public boolean isFull() {
        return full;
    }
    
    // finalize方法，检查被回收前的状态
    protected void finalize() {
        if(full) {
            System.out.println("错误：Tank " + id + " 在满状态时被回收！应该先清空再回收。");
        } else {
            System.out.println("Tank " + id + " 在空状态时被正确回收。");
        }
    }
}

public class Exercise512 {
    public static void main(String[] args) {
        System.out.println("测试Tank类的finalize()方法能否检查回收前状态");
        
        // 创建两个坦克，一个清空后再回收，一个满的状态就回收
        Tank emptyTank = new Tank();
        Tank fullTank = new Tank();
        
        // 将第一个坦克加满再清空
        emptyTank.fill();
        emptyTank.empty();
        
        // 将第二个坦克加满但不清空
        fullTank.fill();
        
        // 将引用设为null，使对象符合垃圾回收条件
        emptyTank = null;
        fullTank = null;
        
        // 请求垃圾回收
        System.out.println("请求垃圾回收...");
        System.gc();
        
        // 给垃圾回收器一些时间
        try {
            Thread.sleep(500);
        } catch(InterruptedException e) {
            e.printStackTrace();
        }
        
        System.out.println("\n测试结论:");
        System.out.println("1. 如果finalize()方法被调用，会检查Tank对象在回收前是否为空");
        System.out.println("2. 对于空Tank，finalize()提示正确回收");
        System.out.println("3. 对于满Tank，finalize()提示错误回收");
        System.out.println("4. 这种方法可以帮助检测资源泄露问题");
    }
} 