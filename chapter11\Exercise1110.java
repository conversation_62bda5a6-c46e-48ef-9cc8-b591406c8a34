package chapter11;

// 练习10：实现一个简单的自定义Map类，模拟HashMap的部分功能

import java.util.ArrayList;
import java.util.List;

public class Exercise1110 {
    public static void main(String[] args) {
        // 创建自定义HashMap
        System.out.println("1. 创建自定义HashMap：");
        MyHashMap<String, Integer> studentScores = new MyHashMap<>();
        
        // 添加键值对
        studentScores.put("张三", 95);
        studentScores.put("李四", 88);
        studentScores.put("王五", 76);
        studentScores.put("赵六", 92);
        
        System.out.println("初始映射大小: " + studentScores.size());
        System.out.println("映射内容: " + studentScores);
        
        // 获取值
        System.out.println("\n2. 获取值：");
        System.out.println("张三的分数: " + studentScores.get("张三"));
        System.out.println("不存在的键: " + studentScores.get("不存在"));
        
        // 检查键是否存在
        System.out.println("\n3. 检查键是否存在：");
        System.out.println("键'李四'是否存在: " + studentScores.containsKey("李四"));
        System.out.println("键'不存在'是否存在: " + studentScores.containsKey("不存在"));
        
        // 更新值
        System.out.println("\n4. 更新值：");
        studentScores.put("王五", 85);
        System.out.println("更新后王五的分数: " + studentScores.get("王五"));
        
        // 删除键值对
        System.out.println("\n5. 删除键值对：");
        Integer removed = studentScores.remove("张三");
        System.out.println("删除的值: " + removed);
        System.out.println("删除后的大小: " + studentScores.size());
        System.out.println("张三的分数: " + studentScores.get("张三"));
        
        // 获取所有键
        System.out.println("\n6. 获取所有键：");
        List<String> allKeys = studentScores.keys();
        System.out.println("所有键: " + allKeys);
        
        // 获取所有值
        System.out.println("\n7. 获取所有值：");
        List<Integer> allValues = studentScores.values();
        System.out.println("所有值: " + allValues);
        
        // 检查空映射
        System.out.println("\n8. 检查空映射：");
        System.out.println("映射是否为空: " + studentScores.isEmpty());
        
        // 清空映射
        System.out.println("\n9. 清空映射：");
        studentScores.clear();
        System.out.println("清空后的大小: " + studentScores.size());
        System.out.println("映射是否为空: " + studentScores.isEmpty());
        
        // 测试哈希冲突
        System.out.println("\n10. 测试哈希冲突：");
        
        // 使用自定义哈希函数，让键"ABC"和"DEF"产生相同的哈希值
        MyHashMap<CollisionKey, String> collisionMap = new MyHashMap<>();
        
        CollisionKey key1 = new CollisionKey("ABC");
        CollisionKey key2 = new CollisionKey("DEF");
        
        collisionMap.put(key1, "值1");
        collisionMap.put(key2, "值2");
        
        System.out.println("两个不同的键产生相同的哈希值:");
        System.out.println("key1哈希值: " + key1.hashCode());
        System.out.println("key2哈希值: " + key2.hashCode());
        System.out.println("key1对应的值: " + collisionMap.get(key1));
        System.out.println("key2对应的值: " + collisionMap.get(key2));
    }
}

// 自定义HashMap实现
class MyHashMap<K, V> {
    private static final int DEFAULT_CAPACITY = 16;
    private static final float LOAD_FACTOR = 0.75f;
    
    private Entry<K, V>[] buckets;
    private int size;
    
    // 构造函数
    @SuppressWarnings("unchecked")
    public MyHashMap() {
        buckets = new Entry[DEFAULT_CAPACITY];
        size = 0;
    }
    
    // 添加或更新键值对
    public V put(K key, V value) {
        if (key == null) {
            throw new IllegalArgumentException("键不能为空");
        }
        
        // 检查负载因子，如果需要则扩容
        if ((float) (size + 1) / buckets.length > LOAD_FACTOR) {
            resize();
        }
        
        int hash = hash(key);
        int index = indexFor(hash, buckets.length);
        
        // 检查是否已存在此键
        Entry<K, V> current = buckets[index];
        while (current != null) {
            if (current.hash == hash && (current.key == key || current.key.equals(key))) {
                // 更新值
                V oldValue = current.value;
                current.value = value;
                return oldValue;
            }
            current = current.next;
        }
        
        // 添加新条目
        addEntry(hash, key, value, index);
        return null;
    }
    
    // 获取键对应的值
    public V get(K key) {
        if (key == null) {
            return null;
        }
        
        int hash = hash(key);
        int index = indexFor(hash, buckets.length);
        
        Entry<K, V> current = buckets[index];
        while (current != null) {
            if (current.hash == hash && (current.key == key || current.key.equals(key))) {
                return current.value;
            }
            current = current.next;
        }
        
        return null;
    }
    
    // 检查键是否存在
    public boolean containsKey(K key) {
        if (key == null) {
            return false;
        }
        
        int hash = hash(key);
        int index = indexFor(hash, buckets.length);
        
        Entry<K, V> current = buckets[index];
        while (current != null) {
            if (current.hash == hash && (current.key == key || current.key.equals(key))) {
                return true;
            }
            current = current.next;
        }
        
        return false;
    }
    
    // 删除键值对
    public V remove(K key) {
        if (key == null) {
            return null;
        }
        
        int hash = hash(key);
        int index = indexFor(hash, buckets.length);
        
        Entry<K, V> prev = null;
        Entry<K, V> current = buckets[index];
        
        while (current != null) {
            if (current.hash == hash && (current.key == key || current.key.equals(key))) {
                // 找到了要删除的条目
                if (prev == null) {
                    // 是链表头
                    buckets[index] = current.next;
                } else {
                    // 不是链表头
                    prev.next = current.next;
                }
                size--;
                return current.value;
            }
            prev = current;
            current = current.next;
        }
        
        return null;
    }
    
    // 获取所有键
    public List<K> keys() {
        List<K> allKeys = new ArrayList<>();
        for (Entry<K, V> bucket : buckets) {
            Entry<K, V> current = bucket;
            while (current != null) {
                allKeys.add(current.key);
                current = current.next;
            }
        }
        return allKeys;
    }
    
    // 获取所有值
    public List<V> values() {
        List<V> allValues = new ArrayList<>();
        for (Entry<K, V> bucket : buckets) {
            Entry<K, V> current = bucket;
            while (current != null) {
                allValues.add(current.value);
                current = current.next;
            }
        }
        return allValues;
    }
    
    // 检查是否为空
    public boolean isEmpty() {
        return size == 0;
    }
    
    // 获取大小
    public int size() {
        return size;
    }
    
    // 清空
    public void clear() {
        for (int i = 0; i < buckets.length; i++) {
            buckets[i] = null;
        }
        size = 0;
    }
    
    // 计算哈希值
    private int hash(K key) {
        int h = key.hashCode();
        // 进一步处理哈希值，类似于HashMap的实现
        return h ^ (h >>> 16);
    }
    
    // 计算索引
    private int indexFor(int hash, int length) {
        // 使用位运算替代取模运算，更高效
        return hash & (length - 1);
    }
    
    // 添加新的条目
    private void addEntry(int hash, K key, V value, int bucketIndex) {
        // 将新条目添加到链表头部
        Entry<K, V> newEntry = new Entry<>(hash, key, value, buckets[bucketIndex]);
        buckets[bucketIndex] = newEntry;
        size++;
    }
    
    // 扩容
    @SuppressWarnings("unchecked")
    private void resize() {
        int newCapacity = buckets.length * 2;
        Entry<K, V>[] newBuckets = new Entry[newCapacity];
        
        // 重新哈希所有条目
        for (Entry<K, V> bucket : buckets) {
            Entry<K, V> current = bucket;
            while (current != null) {
                Entry<K, V> next = current.next;
                // 计算新索引
                int newIndex = indexFor(current.hash, newCapacity);
                
                // 插入到新的桶中
                current.next = newBuckets[newIndex];
                newBuckets[newIndex] = current;
                
                current = next;
            }
        }
        
        buckets = newBuckets;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("{");
        boolean first = true;
        
        for (Entry<K, V> bucket : buckets) {
            Entry<K, V> current = bucket;
            while (current != null) {
                if (!first) {
                    sb.append(", ");
                }
                first = false;
                sb.append(current.key).append("=").append(current.value);
                current = current.next;
            }
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    // 哈希表条目类
    private static class Entry<K, V> {
        final int hash;
        final K key;
        V value;
        Entry<K, V> next;
        
        Entry(int hash, K key, V value, Entry<K, V> next) {
            this.hash = hash;
            this.key = key;
            this.value = value;
            this.next = next;
        }
    }
}

// 自定义产生哈希冲突的键
class CollisionKey {
    private String value;
    
    public CollisionKey(String value) {
        this.value = value;
    }
    
    @Override
    public int hashCode() {
        // 返回固定值，制造哈希冲突
        return 1;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        CollisionKey other = (CollisionKey) obj;
        return value.equals(other.value);
    }
    
    @Override
    public String toString() {
        return value;
    }
} 