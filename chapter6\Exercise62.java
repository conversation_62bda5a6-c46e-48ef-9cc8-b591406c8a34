// 练习62：创建一个具有protected方法的类。尝试在包外调用该protected方法。

public class Exercise62 {
    public static void main(String[] args) {
        
        ProtectedDemo demo = new ProtectedDemo();
        demo.publicMethod(); // 这是可访问的
        demo.protectedMethod(); // 如果在包外或非子类中调用，这会导致错误
    }
}

class ProtectedDemo {
    public void publicMethod() {
        System.out.println("这是一个public方法");
        //protectedMethod(); // 可以从类内部调用protected方法
    }
    
    protected void protectedMethod() {
        System.out.println("这是一个protected方法");
    }
} 