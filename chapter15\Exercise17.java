package chapter15;

import java.util.*;

/**
 * Exercise 17: Study the JDK documentation for EnumSet. You'll see that there's
 * a Static factory method that takes a vararg of elements of the enum. Create a
 * new class called SetOfEnums.java that demonstrates the use of EnumSet, using
 * a vararg method for the initialization.
 */

// Enum for testing EnumSet
enum Pets {
    DOG, CAT, BIRD, FISH, HAMSTER, RABBIT, TURTLE, SNAKE, LIZARD, FERRET
}

// Utility class for creating EnumSets using varargs
class SetOfEnums {
    // Static factory method to create an EnumSet from varargs
    public static <T extends Enum<T>> EnumSet<T> of(T... args) {
        if(args.length == 0) {
            throw new IllegalArgumentException("At least one element required");
        }
        
        // Create the EnumSet using the first element to determine the enum type
        EnumSet<T> result = EnumSet.noneOf(args[0].getDeclaringClass());
        
        // Add all the elements to the set
        for(T t : args) {
            result.add(t);
        }
        
        return result;
    }
}

public class Exercise17 {
    public static void main(String[] args) {
        // Using JDK's EnumSet.of() method
        System.out.println("Using JDK's EnumSet.of():");
        EnumSet<Pets> petsSet1 = EnumSet.of(
            Pets.DOG, Pets.CAT, Pets.BIRD, Pets.FISH
        );
        System.out.println(petsSet1);
        
        // Using our custom SetOfEnums.of() method
        System.out.println("\nUsing our custom SetOfEnums.of():");
        EnumSet<Pets> petsSet2 = SetOfEnums.of(
            Pets.HAMSTER, Pets.RABBIT, Pets.TURTLE, Pets.SNAKE
        );
        System.out.println(petsSet2);
        
        // Testing with a single element
        System.out.println("\nSingle element:");
        EnumSet<Pets> petsSet3 = SetOfEnums.of(Pets.LIZARD);
        System.out.println(petsSet3);
        
        // Testing with all elements
        System.out.println("\nAll elements:");
        EnumSet<Pets> petsSet4 = SetOfEnums.of(
            Pets.DOG, Pets.CAT, Pets.BIRD, Pets.FISH, 
            Pets.HAMSTER, Pets.RABBIT, Pets.TURTLE, 
            Pets.SNAKE, Pets.LIZARD, Pets.FERRET
        );
        System.out.println(petsSet4);
        
        // Comparing with EnumSet.allOf()
        System.out.println("\nComparing with EnumSet.allOf():");
        EnumSet<Pets> allPets = EnumSet.allOf(Pets.class);
        System.out.println(allPets);
        System.out.println("Sets are equal: " + petsSet4.equals(allPets));
        
        // Testing with no elements (should throw exception)
        System.out.println("\nTesting with no elements:");
        try {
            EnumSet<Pets> emptySet = SetOfEnums.of();
            System.out.println(emptySet); // Should not reach here
        } catch(IllegalArgumentException e) {
            System.out.println("Exception caught: " + e.getMessage());
        }
        
        // Other EnumSet methods
        System.out.println("\nOther EnumSet methods:");
        System.out.println("Complement of petsSet1: " + EnumSet.complementOf(petsSet1));
        System.out.println("Range from DOG to FISH: " + EnumSet.range(Pets.DOG, Pets.FISH));
    }
} 