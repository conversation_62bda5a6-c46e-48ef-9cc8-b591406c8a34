// 练习16：创建名为Amphibian的类。由它派生出一个称为Frog的类。
// 在main()中，创建一个Frog，并将其向上转型至Amphibian，然后说明所有方法都可正常工作。

package chapter7;

class Amphibian {
    public void swim() {
        System.out.println("Amphibian swimming");
    }
    
    public void walk() {
        System.out.println("Amphibian walking");
    }
    
    public void breathe() {
        System.out.println("Amphibian breathing");
    }
}

class Frog extends Amphibian {
    // Inherits all methods from Amphibian
}

public class Exercise716 {
    public static void main(String[] args) {
        Frog frog = new Frog();
        
        // Test methods on Frog directly
        System.out.println("Calling methods on Frog reference:");
        frog.swim();
        frog.walk();
        frog.breathe();
        
        // Upcast to Amphibian and test methods
        System.out.println("\nCalling methods on Amphibian reference:");
        Amphibian amphibian = frog; // Upcast
        amphibian.swim();
        amphibian.walk();
        amphibian.breathe();
    }
} 