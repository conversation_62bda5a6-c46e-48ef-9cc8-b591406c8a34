package chapter11;

// 练习6：使用TreeSet实现有序集合，并自定义排序规则

import java.util.Comparator;
import java.util.TreeSet;

public class Exercise116 {
    public static void main(String[] args) {
        // 创建TreeSet - 默认自然排序
        System.out.println("1. 创建默认排序的TreeSet（自然排序）：");
        TreeSet<String> names = new TreeSet<>();
        names.add("张三");
        names.add("李四");
        names.add("王五");
        names.add("赵六");
        names.add("钱七");
        System.out.println("按照字符串自然排序: " + names);
        
        // 使用整数
        TreeSet<Integer> numbers = new TreeSet<>();
        numbers.add(5);
        numbers.add(2);
        numbers.add(10);
        numbers.add(1);
        numbers.add(7);
        System.out.println("整数按照自然排序: " + numbers);
        
        // TreeSet的特殊方法
        System.out.println("\n2. TreeSet的导航方法：");
        System.out.println("第一个元素: " + numbers.first());
        System.out.println("最后一个元素: " + numbers.last());
        System.out.println("小于等于3的最大元素: " + numbers.floor(3));
        System.out.println("大于等于3的最小元素: " + numbers.ceiling(3));
        System.out.println("严格小于3的最大元素: " + numbers.lower(3));
        System.out.println("严格大于3的最小元素: " + numbers.higher(3));
        
        // 获取子集
        System.out.println("\n3. 获取子集：");
        System.out.println("头部集合 [0, 5): " + numbers.headSet(5));
        System.out.println("尾部集合 [5, ∞): " + numbers.tailSet(5));
        System.out.println("子集合 [2, 7): " + numbers.subSet(2, 7));
        
        // 自定义比较器 - 按字符串长度排序
        System.out.println("\n4. 自定义比较器 - 按字符串长度排序：");
        TreeSet<String> lengthSortedSet = new TreeSet<>(new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                // 主要按长度排序
                int result = s1.length() - s2.length();
                // 如果长度相同，则按字母顺序排序，确保不会丢失元素
                return result != 0 ? result : s1.compareTo(s2);
            }
        });
        
        lengthSortedSet.add("苹果");     // 2个字符
        lengthSortedSet.add("香蕉");     // 2个字符
        lengthSortedSet.add("西瓜");     // 2个字符
        lengthSortedSet.add("哈密瓜");   // 3个字符
        lengthSortedSet.add("火龙果");   // 3个字符
        lengthSortedSet.add("葡萄柚");   // 3个字符
        System.out.println("按字符串长度排序: " + lengthSortedSet);
        
        // 使用Lambda表达式创建比较器 - 降序排序
        System.out.println("\n5. 使用Lambda表达式创建降序排序的集合：");
        TreeSet<Integer> descendingSet = new TreeSet<>((a, b) -> b - a);
        descendingSet.addAll(numbers);
        System.out.println("降序排列: " + descendingSet);
        
        // 自定义对象排序
        System.out.println("\n6. 自定义对象排序：");
        TreeSet<Person> people = new TreeSet<>();
        people.add(new Person("张三", 25));
        people.add(new Person("李四", 30));
        people.add(new Person("王五", 20));
        people.add(new Person("赵六", 35));
        System.out.println("按年龄排序的人员列表:");
        for (Person person : people) {
            System.out.println(person);
        }
        
        // 使用外部比较器
        System.out.println("\n7. 使用外部比较器按姓名排序：");
        TreeSet<Person> peopleByName = new TreeSet<>(new NameComparator());
        peopleByName.addAll(people);
        System.out.println("按姓名排序的人员列表:");
        for (Person person : peopleByName) {
            System.out.println(person);
        }
    }
}

// 实现Comparable接口的类 - 按年龄排序
class Person implements Comparable<Person> {
    private String name;
    private int age;
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() {
        return name;
    }
    
    public int getAge() {
        return age;
    }
    
    // 实现compareTo方法，按年龄排序
    @Override
    public int compareTo(Person other) {
        return this.age - other.age;
    }
    
    @Override
    public String toString() {
        return name + " (" + age + "岁)";
    }
}

// 外部比较器 - 按姓名排序
class NameComparator implements Comparator<Person> {
    @Override
    public int compare(Person p1, Person p2) {
        return p1.getName().compareTo(p2.getName());
    }
} 