public class Exercise44 {
    public static void main(String[] args) {
        int limit = 100; // 设定要检测的数字范围，这里是检测 2 到 100 之间的数
        System.out.println("2 到 " + limit + " 之间的素数有:");
        // 外层循环，遍历 2 到 limit 之间的所有数字
        for (int i = 2; i <= limit; i++) {
            boolean isPrime = true;
            // 内层循环，检查当前数字 i 是否为素数
            for (int j = 2; j < i; j++) {
                if (i % j == 0) {
                    // 如果 i 能被 j 整除，说明 i 不是素数
                    isPrime = false;
                    break;
                }
            }
            if (isPrime) {
                // 如果 isPrime 仍然为 true，说明 i 是素数，将其打印出来
                System.out.print(i + " ");
            }
        }
    }
}
