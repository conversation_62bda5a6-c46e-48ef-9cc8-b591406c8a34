// 练习65：创建一个具有public、private、protected和包访问权限的字段和方法的类。在同一个包中创建另一个类，并创建第一个类的实例。你有什么样的访问权限？


public class Exercise65 {
    public static void main(String[] args) {
        System.out.println("练习65：测试同一包内的访问权限。");
        
        // 创建一个具有不同访问修饰符的类的实例
        AccessTest accessTest = new AccessTest();
        
        // 可以访问public成员
        accessTest.publicField = 1;
        accessTest.publicMethod();
        
        // 可以访问同一包中的protected成员
        accessTest.protectedField = 2;
        accessTest.protectedMethod();
        
        // 可以访问同一包中的包访问成员
        accessTest.packageField = 3;
        accessTest.packageMethod();
        
        // 不能访问private成员
        // accessTest.privateField = 4; // 编译错误
        // accessTest.privateMethod(); // 编译错误
    }
}

class AccessTest {
    public int publicField;
    protected int protectedField;
    int packageField; // 包访问权限（无修饰符）
    private int privateField;
    
    public void publicMethod() {
        System.out.println("公共方法");
    }
    
    protected void protectedMethod() {
        System.out.println("受保护方法");
    }
    
    void packageMethod() {
        System.out.println("包访问方法");
    }
    
    private void privateMethod() {
        System.out.println("私有方法");
    }
} 