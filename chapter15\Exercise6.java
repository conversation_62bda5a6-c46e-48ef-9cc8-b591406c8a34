package chapter15;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Exercise 6: Use RandomList with two more types in addition to the one shown in
 * the book (Integer).
 */

// RandomList class from the book
class RandomList<T> {
    private ArrayList<T> storage = new ArrayList<>();
    private Random rand = new Random();
    
    public void add(T item) {
        storage.add(item);
    }
    
    public T select() {
        return storage.get(rand.nextInt(storage.size()));
    }
    
    public int size() {
        return storage.size();
    }
}

public class Exercise6 {
    public static void main(String[] args) {
        // Example 1: RandomList with Integer (as shown in the book)
        RandomList<Integer> intList = new RandomList<>();
        for(int i = 0; i < 20; i++) {
            intList.add(i);
        }
        
        System.out.println("Selecting 10 random integers:");
        for(int i = 0; i < 10; i++) {
            System.out.print(intList.select() + " ");
        }
        System.out.println("\n");
        
        // Example 2: RandomList with String
        RandomList<String> stringList = new RandomList<>();
        String[] words = "The quick brown fox jumps over the lazy dog".split(" ");
        for(String word : words) {
            stringList.add(word);
        }
        
        System.out.println("Selecting 10 random words:");
        for(int i = 0; i < 10; i++) {
            System.out.print(stringList.select() + " ");
        }
        System.out.println("\n");
        
        // Example 3: RandomList with a custom class
        class Person {
            private String name;
            private int age;
            
            public Person(String name, int age) {
                this.name = name;
                this.age = age;
            }
            
            @Override
            public String toString() {
                return name + " (" + age + ")";
            }
        }
        
        RandomList<Person> personList = new RandomList<>();
        List<Person> people = Arrays.asList(
            new Person("Alice", 25),
            new Person("Bob", 30),
            new Person("Charlie", 35),
            new Person("David", 40),
            new Person("Eve", 45)
        );
        
        for(Person person : people) {
            personList.add(person);
        }
        
        System.out.println("Selecting 10 random people:");
        for(int i = 0; i < 10; i++) {
            System.out.println(personList.select());
        }
    }
} 