// 练习611：创建本章中的第一个类。在第二个类中创建第一个类的对象。


public class Exercise611 {
    public static void main(String[] args) {
        System.out.println("练习611：在一个类中创建另一个类的对象。");
        
        SecondClass sc = new SecondClass();
        sc.createAndUseFirstClass();
    }
}

class FirstClass {
    public void showMessage() {
        System.out.println("这是第一个类");
    }
}

class SecondClass {
    public void createAndUseFirstClass() {
        // 创建FirstClass的对象
        FirstClass fc = new FirstClass();
        
        // 使用该对象
        fc.showMessage();
    }
}

class ThirdClass {
    public void createAndUseSecondClass() {
        SecondClass sc = new SecondClass();
        sc.createAndUseFirstClass();
    }
}