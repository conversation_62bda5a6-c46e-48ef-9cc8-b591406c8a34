package chapter14;

/**
 * Exercise 5: Create a generic interface with two type parameters, and implement
 * it with a class that takes three type parameters by extending the third type parameter.
 */
public class Exercise5 {
    public static void main(String[] args) {
        // Test with String, Integer, and Double
        TripleStore<String, Integer, Double> store1 = 
            new TripleStoreImpl<>("Hello", 42, 3.14);
        
        System.out.println("Key: " + store1.getKey());
        System.out.println("Value: " + store1.getValue());
        System.out.println("Extra: " + store1.getExtra());
        
        // Test with different types
        TripleStore<Boolean, Character, String> store2 = 
            new TripleStoreImpl<>(true, 'A', "Testing");
        
        System.out.println("\nKey: " + store2.getKey());
        System.out.println("Value: " + store2.getValue());
        System.out.println("Extra: " + store2.getExtra());
        
        // Modify values
        store2.setKey(false);
        store2.setValue('B');
        store2.setExtra("Modified");
        
        System.out.println("\nModified values:");
        System.out.println("Key: " + store2.getKey());
        System.out.println("Value: " + store2.getValue());
        System.out.println("Extra: " + store2.getExtra());
    }
}

// Generic interface with two type parameters
interface KeyValue<K, V> {
    K getKey();
    void setKey(K key);
    V getValue();
    void setValue(V value);
}

// Extended interface with an additional type parameter
interface TripleStore<K, V, E> extends KeyValue<K, V> {
    E getExtra();
    void setExtra(E extra);
}

// Implementation with three type parameters
class TripleStoreImpl<K, V, E> implements TripleStore<K, V, E> {
    private K key;
    private V value;
    private E extra;
    
    public TripleStoreImpl(K key, V value, E extra) {
        this.key = key;
        this.value = value;
        this.extra = extra;
    }
    
    @Override
    public K getKey() { return key; }
    
    @Override
    public void setKey(K key) { this.key = key; }
    
    @Override
    public V getValue() { return value; }
    
    @Override
    public void setValue(V value) { this.value = value; }
    
    @Override
    public E getExtra() { return extra; }
    
    @Override
    public void setExtra(E extra) { this.extra = extra; }
} 