// 练习5：从Exercise1.java开始，在Cycle中添加一个balance()方法，使其在Unicycle和Bicycle实例中被覆盖，
// 但在Tricycle中不覆盖。创建所有三种类型的实例，并将它们向上转型为Cycle数组。
// 尝试调用balance()方法，解释发生的情况。


class Cycle3 {
    public void ride() {
        System.out.println("Cycle riding");
    }
    
    public void balance() {
        System.out.println("Cycle balance");
    }
}

class Unicycle3 extends Cycle3 {
    @Override
    public void ride() {
        System.out.println("Unicycle riding");
    }
    
    @Override
    public void balance() {
        System.out.println("Unicycle balance: requires skill");
    }
}

class Bicycle3 extends Cycle3 {
    @Override
    public void ride() {
        System.out.println("Bicycle riding");
    }
    
    @Override
    public void balance() {
        System.out.println("Bicycle balance: moderate difficulty");
    }
}

class Tricycle3 extends Cycle3 {
    @Override
    public void ride() {
        System.out.println("Tricycle riding");
    }
    // Doesn't override balance()
}

public class Exercise85 {
    public static void main(String[] args) {
        Cycle3[] cycles = {
            new Unicycle3(),
            new Bicycle3(),
            new Tricycle3()
        };
        
        // Demonstrate polymorphism with ride()
        for(Cycle3 c : cycles) {
            c.ride();
            c.balance(); // Will call appropriate overridden methods
        }
    }
} 