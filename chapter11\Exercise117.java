package chapter11;

// 练习7：使用TreeMap实现有序映射，并演示导航方法

import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;

public class Exercise117 {
    public static void main(String[] args) {
        // 创建TreeMap - 默认按键的自然顺序排序
        System.out.println("1. 创建默认排序的TreeMap：");
        TreeMap<String, Integer> studentScores = new TreeMap<>();
        studentScores.put("张三", 85);
        studentScores.put("李四", 92);
        studentScores.put("王五", 78);
        studentScores.put("赵六", 95);
        studentScores.put("钱七", 88);
        
        // 按键排序打印
        System.out.println("按照学生姓名排序的分数：");
        for (Map.Entry<String, Integer> entry : studentScores.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue() + "分");
        }
        
        // 使用整数键
        TreeMap<Integer, String> rankMap = new TreeMap<>();
        rankMap.put(5, "第五名");
        rankMap.put(1, "第一名");
        rankMap.put(3, "第三名");
        rankMap.put(2, "第二名");
        rankMap.put(4, "第四名");
        
        System.out.println("\n按照排名顺序：");
        for (Map.Entry<Integer, String> entry : rankMap.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        
        // TreeMap的导航方法
        System.out.println("\n2. TreeMap的导航方法：");
        System.out.println("第一个键: " + rankMap.firstKey() + ", 值: " + rankMap.get(rankMap.firstKey()));
        System.out.println("最后一个键: " + rankMap.lastKey() + ", 值: " + rankMap.get(rankMap.lastKey()));
        System.out.println("小于等于3的最大键: " + rankMap.floorKey(3) + ", 值: " + rankMap.get(rankMap.floorKey(3)));
        System.out.println("大于等于3的最小键: " + rankMap.ceilingKey(3) + ", 值: " + rankMap.get(rankMap.ceilingKey(3)));
        System.out.println("严格小于3的最大键: " + rankMap.lowerKey(3) + ", 值: " + rankMap.get(rankMap.lowerKey(3)));
        System.out.println("严格大于3的最小键: " + rankMap.higherKey(3) + ", 值: " + rankMap.get(rankMap.higherKey(3)));
        
        // 获取子映射
        System.out.println("\n3. 获取子映射：");
        System.out.println("头部映射 [min, 3): " + rankMap.headMap(3));
        System.out.println("尾部映射 [3, max): " + rankMap.tailMap(3));
        System.out.println("子映射 [2, 4): " + rankMap.subMap(2, 4));
        
        // 自定义比较器 - 按值排序（成绩降序）
        System.out.println("\n4. 使用自定义比较器 - 按分数降序排序：");
        TreeMap<String, Integer> scoresByValue = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                // 先按分数降序排序
                int scoreCompare = studentScores.get(s2).compareTo(studentScores.get(s1));
                // 如果分数相同，则按姓名升序排序
                if (scoreCompare == 0) {
                    return s1.compareTo(s2);
                }
                return scoreCompare;
            }
        });
        
        // 添加所有学生
        scoresByValue.putAll(studentScores);
        
        // 打印按分数降序排序的结果
        System.out.println("按照分数降序排列的学生：");
        for (Map.Entry<String, Integer> entry : scoresByValue.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue() + "分");
        }
        
        // 处理键值对
        System.out.println("\n5. 键值对处理：");
        Map.Entry<Integer, String> firstEntry = rankMap.firstEntry();
        System.out.println("第一个条目: " + firstEntry.getKey() + " -> " + firstEntry.getValue());
        
        // 移除并返回第一个条目
        Map.Entry<Integer, String> polledEntry = rankMap.pollFirstEntry();
        System.out.println("移除的第一个条目: " + polledEntry.getKey() + " -> " + polledEntry.getValue());
        System.out.println("移除后的映射: " + rankMap);
        
        // 移除并返回最后一个条目
        Map.Entry<Integer, String> polledLastEntry = rankMap.pollLastEntry();
        System.out.println("移除的最后一个条目: " + polledLastEntry.getKey() + " -> " + polledLastEntry.getValue());
        System.out.println("移除后的映射: " + rankMap);
        
        // 使用descendingMap获取降序视图
        System.out.println("\n6. 降序视图：");
        System.out.println("原始映射: " + rankMap);
        System.out.println("降序视图: " + rankMap.descendingMap());
    }
} 