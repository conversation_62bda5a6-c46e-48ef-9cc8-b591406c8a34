package chapter11;

// 练习9：使用Collections工具类，演示集合操作实用方法

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class Exercise119 {
    public static void main(String[] args) {
        // 创建和填充列表
        System.out.println("1. 创建列表：");
        List<Integer> numbers = new ArrayList<>();
        numbers.add(5);
        numbers.add(2);
        numbers.add(8);
        numbers.add(1);
        numbers.add(9);
        numbers.add(3);
        numbers.add(7);
        System.out.println("原始列表: " + numbers);
        
        // 排序
        System.out.println("\n2. 排序：");
        Collections.sort(numbers);
        System.out.println("升序排序: " + numbers);
        
        // 反向排序
        Collections.reverse(numbers);
        System.out.println("反向排序: " + numbers);
        
        // 使用比较器排序
        Collections.sort(numbers, new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return o1.compareTo(o2); // 升序
            }
        });
        System.out.println("使用比较器排序 (升序): " + numbers);
        
        // 二分查找
        System.out.println("\n3. 二分查找（列表必须已排序）：");
        int index = Collections.binarySearch(numbers, 5);
        System.out.println("元素5的索引: " + index);
        
        // 最大和最小值
        System.out.println("\n4. 查找极值：");
        Integer max = Collections.max(numbers);
        Integer min = Collections.min(numbers);
        System.out.println("最大值: " + max);
        System.out.println("最小值: " + min);
        
        // 使用自定义比较器查找极值
        Integer customMax = Collections.max(numbers, Collections.reverseOrder());
        System.out.println("使用反向比较器的最大值（实际上是最小值）: " + customMax);
        
        // 随机打乱
        System.out.println("\n5. 随机打乱：");
        Collections.shuffle(numbers);
        System.out.println("打乱后: " + numbers);
        
        // 复制列表
        System.out.println("\n6. 复制列表：");
        List<Integer> destination = Arrays.asList(0, 0, 0, 0, 0, 0, 0); // 必须有足够的空间
        Collections.copy(destination, numbers);
        System.out.println("复制后的列表: " + destination);
        
        // 替换所有元素
        System.out.println("\n7. 替换元素：");
        Collections.replaceAll(numbers, 7, 70);
        System.out.println("替换7为70后: " + numbers);
        
        // 填充列表
        System.out.println("\n8. 填充列表：");
        Collections.fill(destination, 0);
        System.out.println("填充为0后: " + destination);
        
        // 旋转列表
        System.out.println("\n9. 旋转列表：");
        System.out.println("旋转前: " + numbers);
        Collections.rotate(numbers, 2);  // 向右旋转2个位置
        System.out.println("向右旋转2个位置后: " + numbers);
        
        // 交换元素
        System.out.println("\n10. 交换元素：");
        Collections.swap(numbers, 0, numbers.size() - 1);
        System.out.println("交换第一个和最后一个元素后: " + numbers);
        
        // 频率统计
        System.out.println("\n11. 频率统计：");
        List<String> words = Arrays.asList("apple", "banana", "apple", "orange", "banana", "apple");
        int frequency = Collections.frequency(words, "apple");
        System.out.println("'apple'出现的次数: " + frequency);
        
        // 不可修改的视图
        System.out.println("\n12. 不可修改的视图：");
        List<Integer> unmodifiableList = Collections.unmodifiableList(numbers);
        System.out.println("不可修改的列表: " + unmodifiableList);
        System.out.println("尝试修改会抛出UnsupportedOperationException");
        
        // 单例集合
        System.out.println("\n13. 单例集合：");
        List<String> singletonList = Collections.singletonList("单个元素");
        System.out.println("单例列表: " + singletonList);
        
        // 空集合
        List<Object> emptyList = Collections.emptyList();
        System.out.println("空列表: " + emptyList);
        
        // 二分法排序
        System.out.println("\n14. 二分法排序（对几乎已排序的大列表效率更高）：");
        List<Integer> almostSorted = new ArrayList<>(Arrays.asList(1, 2, 3, 7, 5, 6, 4, 8, 9, 10));
        System.out.println("几乎已排序的列表: " + almostSorted);
        Collections.sort(almostSorted);  // 这里用普通排序，效果相同
        System.out.println("排序后: " + almostSorted);
        
        // 使用同步包装器
        System.out.println("\n15. 同步包装器：");
        List<Integer> syncList = Collections.synchronizedList(new ArrayList<>());
        syncList.addAll(numbers);
        System.out.println("同步列表(线程安全): " + syncList);
    }
} 