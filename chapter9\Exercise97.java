// Exercise 7: Change Exercise 9 in the Polymorphism chapter 
// to use an interface called Instrument.

interface Instrument {
    void play();
    String what();
    void adjust();
}

class Wind implements Instrument {
    @Override
    public void play() {
        System.out.println("Wind.play()");
    }
    
    @Override
    public String what() {
        return "Wind";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Wind");
    }
}

class Percussion implements Instrument {
    @Override
    public void play() {
        System.out.println("Percussion.play()");
    }
    
    @Override
    public String what() {
        return "Percussion";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Percussion");
    }
}

class Stringed implements Instrument {
    @Override
    public void play() {
        System.out.println("Stringed.play()");
    }
    
    @Override
    public String what() {
        return "Stringed";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Stringed");
    }
}

class Brass extends Wind {
    @Override
    public void play() {
        System.out.println("Brass.play()");
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Brass");
    }
}

class Woodwind extends Wind {
    @Override
    public void play() {
        System.out.println("Woodwind.play()");
    }
    
    @Override
    public String what() {
        return "Woodwind";
    }
}

public class Exercise97 {
    static void tune(Instrument i) {
        i.play();
    }
    
    static void tuneAll(Instrument[] e) {
        for(Instrument i : e)
            tune(i);
    }
    
    public static void main(String[] args) {
        Instrument[] orchestra = {
            new Wind(),
            new Percussion(),
            new Stringed(),
            new Brass(),
            new Woodwind()
        };
        tuneAll(orchestra);
        
        System.out.println("\nExercise 7 completed successfully");
    }
} 