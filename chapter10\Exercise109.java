// Exercise 9: Create an interface with at least one method, and implement it 
// by defining an inner class within a method that returns a reference to your interface.

interface Service9 {
    void doService();
    String getServiceDescription();
}

public class Exercise109 {
    // Method that defines an inner class and returns a reference to the interface
    public Service9 getService(final String description) {
        // Inner class defined inside a method
        class ServiceImplementation implements Service9 {
            @Override
            public void doService() {
                System.out.println("Service implementation performing service");
            }
            
            @Override
            public String getServiceDescription() {
                return description;
            }
        }
        
        return new ServiceImplementation();
    }
    
    public static void main(String[] args) {
        Exercise109 ex = new Exercise109();
        
        // Get interface reference from method
        Service9 service = ex.getService("Test service");
        
        // Use the interface methods
        System.out.println("Service description: " + service.getServiceDescription());
        service.doService();
        
        System.out.println("\nExercise 9 completed successfully");
    }
} 