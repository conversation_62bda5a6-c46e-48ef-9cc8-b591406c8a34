// Exercise 1: Create an interface with at least one method, in its own package. 
// Create a class in a separate package. Add a protected method to your class 
// that produces an instance of your interface. Create a second class in the 
// second package, and call the protected method. Verify that the protected 
// method is inaccessible to the general client programmer.

// Note: For simplicity, we're implementing this in a single file,
// but conceptually these would be in separate packages.

// This would be in package interfaces
interface Service {
    void method1();
    void method2();
}

// This would be in package serviceimpl
class Implementation1 implements Service {
    @Override
    public void method1() {
        System.out.println("Implementation1 method1");
    }

    @Override
    public void method2() {
        System.out.println("Implementation1 method2");
    }
    
    protected Service createService() {
        return new Implementation1();
    }
    
    public Service getService() {
        return new Implementation1();
    }
}

// This would be in package serviceimpl
class Implementation1Factory {
    public Service getService() {
        return new Implementation1();
    }
}

// This demonstrates the client code
public class Exercise91 {
    public static void main(String[] args) {
        // This works because we're using the public factory method
        Implementation1Factory factory = new Implementation1Factory();
        Service service = factory.getService();
        service.method1();
        service.method2();
        
        // This works because we're using the public method from Implementation1
        Implementation1 impl1 = new Implementation1();
        Service anotherService = impl1.getService();
        anotherService.method1();
        anotherService.method2();
        
        // The following would not compile if uncommented because
        // createService() is protected and not accessible from a different package:
        // Service s = impl1.createService(); // Error: protected access
        
        System.out.println("Exercise 1 completed successfully");
    }
} 