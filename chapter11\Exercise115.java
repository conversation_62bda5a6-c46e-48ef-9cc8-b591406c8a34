package chapter11;

// 练习5：使用LinkedList实现队列和栈，演示其双端队列的特性

import java.util.LinkedList;

public class Exercise115 {
    public static void main(String[] args) {
        System.out.println("1. 创建LinkedList并添加元素：");
        LinkedList<String> linkedList = new LinkedList<>();
        
        // 添加元素
        linkedList.add("元素1");
        linkedList.add("元素2");
        linkedList.add("元素3");
        System.out.println("初始链表: " + linkedList);
        
        // 在开头和结尾添加元素
        System.out.println("\n2. 在链表两端添加元素：");
        linkedList.addFirst("开头元素");
        linkedList.addLast("结尾元素");
        System.out.println("添加后的链表: " + linkedList);
        
        // 在指定位置添加元素
        System.out.println("\n3. 在指定位置添加元素：");
        linkedList.add(2, "插入的元素");
        System.out.println("在索引2处插入后: " + linkedList);
        
        // 访问元素
        System.out.println("\n4. 获取链表中的元素：");
        System.out.println("第一个元素: " + linkedList.getFirst());
        System.out.println("最后一个元素: " + linkedList.getLast());
        System.out.println("索引2处的元素: " + linkedList.get(2));
        
        // 修改元素
        System.out.println("\n5. 修改元素：");
        linkedList.set(1, "修改后的元素");
        System.out.println("修改索引1后的链表: " + linkedList);
        
        // 查找元素
        System.out.println("\n6. 查找元素：");
        System.out.println("'插入的元素'的索引: " + linkedList.indexOf("插入的元素"));
        System.out.println("链表是否包含'元素3': " + linkedList.contains("元素3"));
        
        // 演示队列操作
        System.out.println("\n7. 使用LinkedList作为队列（先进先出）：");
        LinkedList<String> queue = new LinkedList<>();
        
        // 入队操作
        queue.offer("任务1");
        queue.offer("任务2");
        queue.offer("任务3");
        System.out.println("初始队列: " + queue);
        
        // 查看队首但不移除
        System.out.println("队首元素 (peek): " + queue.peek());
        
        // 出队操作
        System.out.println("出队 (poll): " + queue.poll());
        System.out.println("出队后的队列: " + queue);
        System.out.println("再次出队 (poll): " + queue.poll());
        System.out.println("再次出队后的队列: " + queue);
        
        // 演示栈操作
        System.out.println("\n8. 使用LinkedList作为栈（后进先出）：");
        LinkedList<String> stack = new LinkedList<>();
        
        // 入栈操作
        stack.push("元素A");
        stack.push("元素B");
        stack.push("元素C");
        System.out.println("初始栈: " + stack);
        
        // 查看栈顶但不移除
        System.out.println("栈顶元素 (peek): " + stack.peek());
        
        // 出栈操作
        System.out.println("出栈 (pop): " + stack.pop());
        System.out.println("出栈后的栈: " + stack);
        System.out.println("再次出栈 (pop): " + stack.pop());
        System.out.println("再次出栈后的栈: " + stack);
        
        // 清除所有元素
        System.out.println("\n9. 移除元素：");
        linkedList.removeFirst();
        System.out.println("移除第一个元素后: " + linkedList);
        linkedList.removeLast();
        System.out.println("移除最后一个元素后: " + linkedList);
        linkedList.remove(1);
        System.out.println("移除索引1处元素后: " + linkedList);
        linkedList.remove("元素3");
        System.out.println("移除'元素3'后: " + linkedList);
        
        // 清空链表
        linkedList.clear();
        System.out.println("\n10. 清空后的链表: " + linkedList);
        System.out.println("链表是否为空: " + linkedList.isEmpty());
    }
} 