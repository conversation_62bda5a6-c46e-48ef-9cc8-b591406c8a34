package chapter14;

/**
 * Exercise 10: Create a recursive generic data structure (like a linked list)
 * and demonstrate its use.
 */
public class Exercise10 {
    public static void main(String[] args) {
        // Create a linked list of integers
        LinkedNode<Integer> intList = new LinkedNode<>(1);
        intList.add(2).add(3).add(4).add(5);
        
        System.out.println("Integer list:");
        intList.printList();
        
        // Create a linked list of strings
        LinkedNode<String> stringList = new LinkedNode<>("one");
        stringList.add("two").add("three").add("four").add("five");
        
        System.out.println("\nString list:");
        stringList.printList();
        
        // Demonstrate getting values
        System.out.println("\nGetting values:");
        System.out.println("First integer: " + intList.getValue());
        System.out.println("Second integer: " + intList.getNext().getValue());
        System.out.println("First string: " + stringList.getValue());
        System.out.println("Second string: " + stringList.getNext().getValue());
        
        // Demonstrate finding a value
        System.out.println("\nFinding values:");
        System.out.println("Contains 3: " + intList.contains(3));
        System.out.println("Contains 6: " + intList.contains(6));
        System.out.println("Contains 'three': " + stringList.contains("three"));
        System.out.println("Contains 'six': " + stringList.contains("six"));
    }
}

// A generic linked list node with recursive type reference
class LinkedNode<T> {
    private T value;
    private LinkedNode<T> next;
    
    public LinkedNode(T value) {
        this.value = value;
        this.next = null;
    }
    
    public T getValue() {
        return value;
    }
    
    public LinkedNode<T> getNext() {
        return next;
    }
    
    // Returns this to allow method chaining
    public LinkedNode<T> add(T value) {
        if (next == null) {
            next = new LinkedNode<>(value);
        } else {
            next.add(value);
        }
        return this;
    }
    
    public boolean contains(T value) {
        if (this.value.equals(value)) {
            return true;
        }
        if (next == null) {
            return false;
        }
        return next.contains(value);
    }
    
    public void printList() {
        System.out.print(value);
        if (next != null) {
            System.out.print(" -> ");
            next.printList();
        } else {
            System.out.println();
        }
    }
} 