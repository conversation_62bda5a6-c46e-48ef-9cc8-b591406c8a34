// Exercise 6: Prove that all the methods in an interface are automatically public.

interface AllPublic {
    void method1();
    String method2();
    int method3(int x);
    // void private method4(); // Won't compile - private not allowed
    // protected void method5(); // Won't compile - protected not allowed
    // The following is redundant - methods are public by default
    public void method6();
}

class PublicMethodsImpl implements AllPublic {
    // Must be public, even though we don't specify public here:
    @Override
    public void method1() {
        System.out.println("method1");
    }
    
    // Must be public:
    @Override
    public String method2() {
        System.out.println("method2");
        return "method2 return value";
    }
    
    // Must be public:
    @Override
    public int method3(int x) {
        System.out.println("method3 with value: " + x);
        return x * 2;
    }
    
    // Already specified as public in the interface:
    @Override
    public void method6() {
        System.out.println("method6");
    }
    
    // This would not compile:
    // void method1() { } // Cannot reduce visibility from public to package
    // protected void method1() { } // Cannot reduce visibility from public to protected
    // private void method1() { } // Cannot reduce visibility from public to private
}

public class Exercise96 {
    public static void main(String[] args) {
        AllPublic ap = new PublicMethodsImpl();
        ap.method1();
        ap.method2();
        ap.method3(10);
        ap.method6();
        
        // Reflection can also prove they're all public
        System.out.println("\nProving methods are public using reflection:");
        try {
            Class<?> c = Class.forName("AllPublic");
            java.lang.reflect.Method[] methods = c.getMethods();
            for(java.lang.reflect.Method m : methods) {
                System.out.println(m.getName() + " is public: " + 
                    java.lang.reflect.Modifier.isPublic(m.getModifiers()));
            }
        } catch(ClassNotFoundException e) {
            System.out.println("Interface not found");
        }
        
        System.out.println("\nExercise 6 completed successfully");
    }
} 