// 练习15：在包内创建一个有protected方法的类。在包外，试着调用protected方法，
// 并解释结果。

package chapter7;

public class Exercise715 {
    protected void f() {
        System.out.println("Protected method in Exercise715");
    }
    
    public static void main(String[] args) {
        Exercise715 e = new Exercise715();
        e.f(); // OK to call from same class
        
        // Create an instance of a class in the same package
        Exercise15Accessor accessor = new Exercise15Accessor();
        accessor.accessProtected(e);
    }
}

class Exercise15Accessor {
    public void accessProtected(Exercise715 e) {
        // Access protected method from same package
        e.f();
    }
} 