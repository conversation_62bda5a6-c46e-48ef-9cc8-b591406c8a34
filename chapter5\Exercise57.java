/**
 * 练习7：创建一个没有构造器的类，并在main()中创建该类的一个对象，
 * 以此验证编译器是否真的会创建一个默认构造器。
 */
class NoConstructor {
    // 这个类没有显式定义任何构造器
    String message = "这个对象是通过默认构造器创建的";
    
    void showMessage() {
        System.out.println(message);
    }
}

public class Exercise57 {
    public static void main(String[] args) {
        // 创建NoConstructor类的对象
        // 如果能成功创建对象，说明编译器确实创建了默认构造器
        NoConstructor nc = new NoConstructor();
        
        // 通过调用方法验证对象已经被正确创建
        nc.showMessage();
        
        System.out.println("验证结果：编译器确实创建了默认构造器，否则上面的对象创建语句将无法编译");
    }
} 