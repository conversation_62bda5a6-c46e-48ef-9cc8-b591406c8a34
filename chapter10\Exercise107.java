// Exercise 7: Create a class with a private field and a private method. 
// Create an inner class with a method that modifies the outer-class field 
// and calls the outer-class method. In a second outer-class method, create an 
// object of the inner class and call its method, then show the effect on the 
// outer-class object.

public class Exercise107 {
    private int outerField = 0;
    private String message = "Initial message";
    
    private void outerMethod() {
        System.out.println("Outer method called with message: " + message);
    }
    
    // Inner class with a method that modifies the outer class field and calls outer method
    class Inner {
        void modifyOuter(int newValue, String newMessage) {
            outerField = newValue;
            message = newMessage;
            outerMethod(); // Call the private method of the outer class
        }
    }
    
    // Outer class method that creates an inner class object and calls its method
    public void testInner() {
        System.out.println("Before inner class modification:");
        System.out.println("outerField = " + outerField);
        System.out.println("message = " + message);
        
        Inner inner = new Inner();
        inner.modifyOuter(47, "Message modified by inner class");
        
        System.out.println("\nAfter inner class modification:");
        System.out.println("outerField = " + outerField);
        System.out.println("message = " + message);
    }
    
    public static void main(String[] args) {
        Exercise107 outer = new Exercise107();
        outer.testInner();
        
        System.out.println("\nExercise 7 completed successfully");
    }
} 