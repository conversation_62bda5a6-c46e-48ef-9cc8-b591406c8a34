// Exercise 8: Determine whether an inner class has access to the 
// private elements of its outer class.

public class Exercise108 {
    private int privateField = 42;
    private static int privateStaticField = 43;
    
    private void privateMethod() {
        System.out.println("Outer class private method called");
    }
    
    private static void privateStaticMethod() {
        System.out.println("Outer class private static method called");
    }
    
    // Inner class to demonstrate access to private elements
    class Inner {
        public void accessOuterPrivates() {
            System.out.println("Inner class accessing outer class private field: " + privateField);
            System.out.println("Inner class accessing outer class private static field: " + privateStaticField);
            
            // Call private methods of the outer class
            System.out.println("Inner class calling outer class private method:");
            privateMethod();
            
            System.out.println("Inner class calling outer class private static method:");
            privateStaticMethod();
            
            // Can also modify private fields
            privateField = 100;
            privateStaticField = 200;
            
            System.out.println("After modification:");
            System.out.println("privateField = " + privateField);
            System.out.println("privateStaticField = " + privateStaticField);
        }
    }
    
    // Static inner class to demonstrate access to private static elements
    static class StaticInner {
        public void accessOuterPrivateStatics() {
            // Can access private static members
            System.out.println("Static inner class accessing outer class private static field: " + privateStaticField);
            
            // Call private static method
            System.out.println("Static inner class calling outer class private static method:");
            privateStaticMethod();
            
            // Can modify private static field
            privateStaticField = 300;
            System.out.println("After modification: privateStaticField = " + privateStaticField);
            
            // Cannot access non-static private members
            // System.out.println(privateField); // Error
            // privateMethod(); // Error
        }
    }
    
    public static void main(String[] args) {
        Exercise108 outer = new Exercise108();
        
        System.out.println("Testing inner class access to private members:");
        Inner inner = outer.new Inner();
        inner.accessOuterPrivates();
        
        System.out.println("\nTesting static inner class access to private static members:");
        StaticInner staticInner = new StaticInner();
        staticInner.accessOuterPrivateStatics();
        
        System.out.println("\nExercise 8 completed successfully");
        System.out.println("Conclusion: Inner classes have access to ALL private elements of the outer class.");
        System.out.println("Static inner classes have access to private STATIC elements of the outer class.");
    }
} 