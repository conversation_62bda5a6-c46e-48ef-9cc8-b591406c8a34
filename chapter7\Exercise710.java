// 练习10：创建一个带有finalize()方法的类，它打印出一条消息。
// 在main()中，创建该类的一个对象。解释这个程序的行为。

package chapter7;

class WithFinalize {
    protected void finalize() {
        System.out.println("WithFinalize finalized");
    }
}

public class Exercise710 {
    public static void main(String[] args) {
        WithFinalize wf = new WithFinalize();
        System.out.println("WithFinalize object created");
        // Try to force garbage collection:
        System.out.println("Attempting to force garbage collection...");
        System.gc();
        System.runFinalization();
        System.out.println("End of main()");
    }
} 