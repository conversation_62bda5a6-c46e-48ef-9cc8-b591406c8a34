import static net.mindview.util.Print.print;
//E10
public class BinaryOps {
	public static void main(String[] args) {
		int i = 1;
		int j = 2;
		print("i = " + Integer.toBinaryString(i));
		print("j = " + Integer.toBinaryString(j));
		print("i & j = " + Integer.toBinaryString(i & j));
		print("i | j = " + Integer.toBinaryString(i | j));
		print("i ^ j = " + Integer.toBinaryString(i ^ j));
		print("~i = " + Integer.toBinaryString(~i));
		print("~j = " + Integer.toBinaryString(~j));
	}
}