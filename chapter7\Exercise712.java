// 练习12：向Exercise9.java中所有的类添加适当的dispose()方法层次结构。

package chapter7;

class Component1A {
    Component1A() {
        System.out.println("Component1A constructor");
    }
    
    void dispose() {
        System.out.println("Component1A dispose");
    }
}

class Component2A {
    Component2A() {
        System.out.println("Component2A constructor");
    }
    
    void dispose() {
        System.out.println("Component2A dispose");
    }
}

class Component3A {
    Component3A() {
        System.out.println("Component3A constructor");
    }
    
    void dispose() {
        System.out.println("Component3A dispose");
    }
}

class RootA {
    Component1A c1 = new Component1A();
    Component2A c2 = new Component2A();
    Component3A c3 = new Component3A();
    
    RootA() {
        System.out.println("RootA constructor");
    }
    
    void dispose() {
        System.out.println("RootA dispose");
        c3.dispose(); // Dispose in reverse order of creation
        c2.dispose();
        c1.dispose();
    }
}

class StemA extends RootA {
    Component1A c1 = new Component1A();
    Component2A c2 = new Component2A();
    Component3A c3 = new Component3A();
    
    StemA() {
        System.out.println("StemA constructor");
    }
    
    void dispose() {
        System.out.println("StemA dispose");
        c3.dispose(); // Dispose in reverse order of creation
        c2.dispose();
        c1.dispose();
        super.dispose(); // Then dispose the base class
    }
}

public class Exercise712 {
    public static void main(String[] args) {
        StemA s = new StemA();
        try {
            // Do something with s...
            System.out.println("Using StemA");
        } finally {
            s.dispose();
        }
    }
} 