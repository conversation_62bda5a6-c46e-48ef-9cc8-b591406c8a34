// 练习3：创建一个包含两个方法的基类，在导出类中覆盖一个方法。创建一个对象，将其向上转型到基类，然后调用这两个方法。解释发生的情况。



class Shape {
    public void draw() {
        System.out.println("Shape.draw()");
    }
    
    public void erase() {
        System.out.println("Shape.erase()");
    }
}

class Circle extends Shape {
    @Override
    public void draw() {
        System.out.println("Circle.draw()");
    }
    // Doesn't override erase()
}

public class Exercise83 {
    public static void main(String[] args) {
        Circle circle = new Circle();
        
        // Upcast to base class Shape
        Shape shape = circle;
        
        // Call both methods
        shape.draw();  // Will call Circle.draw() due to polymorphism
        shape.erase(); // Will call Shape.erase() as it was not overridden
    }
} 