package chapter13;

/**
 * Exercise 13.1: Thread Creation and Basic Operations
 * 
 * This exercise demonstrates different ways to create and start threads in Java:
 * 1. Extending the Thread class
 * 2. Implementing the Runnable interface
 * 3. Using Thread states and basic operations
 */
public class Exercise131 {
    
    public static void main(String[] args) {
        System.out.println("==== Thread Creation and Basic Operations ====");
        
        // 1. Creating a thread by extending Thread class
        System.out.println("\n1. Creating thread by extending Thread class:");
        ThreadExtension thread1 = new ThreadExtension("Thread-Extension");
        thread1.start();
        
        // 2. Creating a thread by implementing Runnable interface
        System.out.println("\n2. Creating thread by implementing Runnable interface:");
        RunnableImplementation runnableTask = new RunnableImplementation();
        Thread thread2 = new Thread(runnableTask, "Runnable-Thread");
        thread2.start();
        
        // 3. Creating a thread using lambda expression (Java 8+)
        System.out.println("\n3. Creating thread using lambda expression:");
        Thread thread3 = new Thread(() -> {
            System.out.println("Lambda thread is running");
            for (int i = 1; i <= 3; i++) {
                System.out.println("Lambda thread count: " + i);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    System.out.println("Lambda thread interrupted");
                }
            }
            System.out.println("Lambda thread finished");
        }, "Lambda-Thread");
        thread3.start();
        
        // 4. Thread states and operations
        System.out.println("\n4. Thread states and operations:");
        Thread thread4 = new Thread(() -> {
            try {
                System.out.println("Thread4 is running");
                Thread.sleep(2000);
                System.out.println("Thread4 finished");
            } catch (InterruptedException e) {
                System.out.println("Thread4 was interrupted");
            }
        }, "Operation-Thread");
        
        System.out.println("Thread4 state before start: " + thread4.getState());
        thread4.start();
        System.out.println("Thread4 state after start: " + thread4.getState());
        
        try {
            Thread.sleep(500);
            System.out.println("Thread4 state while sleeping: " + thread4.getState());
            
            // Join example - wait for thread4 to complete
            System.out.println("Main thread waiting for Thread4 to complete...");
            thread4.join(3000); // Wait at most 3 seconds
            System.out.println("Thread4 state after join: " + thread4.getState());
            
        } catch (InterruptedException e) {
            System.out.println("Main thread was interrupted");
        }
        
        // 5. Thread priority
        System.out.println("\n5. Thread priority:");
        Thread minPriorityThread = new Thread(() -> {
            System.out.println("Minimum priority thread is running");
        });
        Thread maxPriorityThread = new Thread(() -> {
            System.out.println("Maximum priority thread is running");
        });
        
        minPriorityThread.setPriority(Thread.MIN_PRIORITY); // 1
        maxPriorityThread.setPriority(Thread.MAX_PRIORITY); // 10
        
        System.out.println("Min priority thread priority: " + minPriorityThread.getPriority());
        System.out.println("Max priority thread priority: " + maxPriorityThread.getPriority());
        
        minPriorityThread.start();
        maxPriorityThread.start();
        
        // Wait for all threads to finish
        try {
            thread1.join();
            thread2.join();
            thread3.join();
            minPriorityThread.join();
            maxPriorityThread.join();
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted while waiting for threads to finish");
        }
        
        System.out.println("\nAll threads have completed execution!");
    }
}

// Example of extending Thread class
class ThreadExtension extends Thread {
    
    public ThreadExtension(String name) {
        super(name);
    }
    
    @Override
    public void run() {
        System.out.println(getName() + " is running");
        for (int i = 1; i <= 3; i++) {
            System.out.println(getName() + " count: " + i);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                System.out.println(getName() + " interrupted");
            }
        }
        System.out.println(getName() + " finished");
    }
}

// Example of implementing Runnable interface
class RunnableImplementation implements Runnable {
    
    @Override
    public void run() {
        String threadName = Thread.currentThread().getName();
        System.out.println(threadName + " is running");
        for (int i = 1; i <= 3; i++) {
            System.out.println(threadName + " count: " + i);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                System.out.println(threadName + " interrupted");
            }
        }
        System.out.println(threadName + " finished");
    }
} 