package chapter12;

// 练习3：使用字符流（FileReader、FileWriter）进行文本文件的读写

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Exercise123 {
    public static void main(String[] args) {
        // 定义文件路径
        File textFile = new File("chapter12/textfile.txt");
        File copyFile = new File("chapter12/textfile_copy.txt");
        
        try {
            // 确保目录存在
            textFile.getParentFile().mkdirs();
            
            // 1. 使用FileWriter写入文本数据
            System.out.println("1. 使用FileWriter写入文本：");
            
            try (FileWriter writer = new FileWriter(textFile)) {
                // 写入多行文本
                writer.write("这是第一行文本。\n");
                writer.write("This is the second line of text.\n");
                writer.write("这是第三行，包含中文和English混合内容。\n");
                writer.write("数字: 1234567890\n");
                writer.write("特殊字符: !@#$%^&*()_+");
                
                System.out.println("成功写入文本到文件: " + textFile.getPath());
            }
            
            // 2. 使用FileReader读取文本数据
            System.out.println("\n2. 使用FileReader读取文本：");
            
            try (FileReader reader = new FileReader(textFile)) {
                char[] buffer = new char[1024];
                int charsRead = reader.read(buffer);
                
                System.out.println("读取到的文本内容：");
                System.out.println("--------------------");
                System.out.println(new String(buffer, 0, charsRead));
                System.out.println("--------------------");
                System.out.println("成功读取 " + charsRead + " 个字符");
            }
            
            // 3. 使用BufferedWriter增强写入性能
            System.out.println("\n3. 使用BufferedWriter写入文本：");
            
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(copyFile))) {
                // 写入多行文本
                writer.write("使用BufferedWriter写入的第一行");
                writer.newLine(); // 写入换行符，比手动写入\n更具平台独立性
                writer.write("使用BufferedWriter写入的第二行");
                writer.newLine();
                
                // 写入一个字符数组
                char[] charArray = {'字', '符', '数', '组', '内', '容'};
                writer.write(charArray);
                writer.newLine();
                
                // 写入字符数组的一部分
                writer.write(charArray, 2, 3); // 从索引2开始写入3个字符
                writer.newLine();
                
                // 写入一个较长的文本，测试缓冲效果
                for (int i = 1; i <= 10; i++) {
                    writer.write("这是第 " + i + " 行缓冲内容");
                    writer.newLine();
                }
                
                System.out.println("成功使用BufferedWriter写入文本到文件: " + copyFile.getPath());
            }
            
            // 4. 使用BufferedReader增强读取性能和功能
            System.out.println("\n4. 使用BufferedReader逐行读取文本：");
            
            try (BufferedReader reader = new BufferedReader(new FileReader(copyFile))) {
                String line;
                int lineCount = 0;
                
                System.out.println("逐行读取的内容：");
                System.out.println("--------------------");
                
                // readLine()方法可以逐行读取文本
                while ((line = reader.readLine()) != null) {
                    lineCount++;
                    System.out.println("第 " + lineCount + " 行: " + line);
                }
                
                System.out.println("--------------------");
                System.out.println("总共读取了 " + lineCount + " 行文本");
            }
            
            // 5. 使用PrintWriter进行格式化输出
            System.out.println("\n5. 使用PrintWriter进行格式化输出：");
            
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(textFile, true))) {
                // 追加模式，添加一个空行
                printWriter.println();
                
                // 使用println方法自动添加换行
                printWriter.println("以下是使用PrintWriter写入的格式化内容：");
                
                // 格式化输出
                printWriter.printf("格式化数字: %d, %d, %d%n", 10, 20, 30);
                printWriter.printf("格式化浮点数: %.2f, %.3f%n", 3.14159, 2.71828);
                printWriter.printf("格式化字符串: %s, %S%n", "小写", "大写");
                printWriter.printf("格式化日期: %1$tY-%1$tm-%1$td%n", System.currentTimeMillis());
                
                // 使用print方法不添加换行
                printWriter.print("这行不会自动换行");
                printWriter.print("会和上一行连在一起");
                
                printWriter.println(); // 手动添加换行
                printWriter.println("这是最后一行");
                
                System.out.println("成功使用PrintWriter写入格式化内容到: " + textFile.getPath());
            }
            
            // 6. 读取包含格式化内容的文件
            System.out.println("\n6. 读取包含格式化内容的文件：");
            
            try (BufferedReader reader = new BufferedReader(new FileReader(textFile))) {
                String line;
                
                System.out.println("文件完整内容：");
                System.out.println("--------------------");
                
                while ((line = reader.readLine()) != null) {
                    System.out.println(line);
                }
                
                System.out.println("--------------------");
            }
            
            // 7. 使用FileWriter的追加模式
            System.out.println("\n7. 使用FileWriter的追加模式：");
            
            try (FileWriter writer = new FileWriter(textFile, true)) {  // true表示追加模式
                writer.write("\n\n这是使用FileWriter追加模式添加的内容。");
                writer.write("\n文件末尾的最后一行。");
                
                System.out.println("成功追加内容到文件: " + textFile.getPath());
            }
            
            // 8. 字符流处理中文字符
            System.out.println("\n8. 字符流处理中文字符：");
            
            // 创建一个包含大量中文的文本文件
            File chineseFile = new File("chapter12/chinese.txt");
            
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(chineseFile))) {
                writer.write("这是一个测试中文处理的文件。\n");
                writer.write("字符流可以正确处理Unicode字符，包括中文、日文和其他非ASCII字符。\n");
                writer.write("示例中文字符：");
                writer.newLine();
                
                // 写入一些常用汉字
                for (int i = 0; i < 50; i++) {
                    // 生成一些中文字符的Unicode码点
                    char c = (char) (0x4e00 + i);
                    writer.write(c);
                    if ((i + 1) % 10 == 0) {
                        writer.newLine();
                    }
                }
                
                System.out.println("成功创建中文文本文件: " + chineseFile.getPath());
            }
            
            // 读取中文文件并统计字符
            try (BufferedReader reader = new BufferedReader(new FileReader(chineseFile))) {
                String line;
                int charCount = 0;
                int lineCount = 0;
                
                while ((line = reader.readLine()) != null) {
                    lineCount++;
                    charCount += line.length();
                }
                
                System.out.println("中文文件统计：");
                System.out.println("总行数: " + lineCount);
                System.out.println("总字符数: " + charCount);
            }
            
            // 9. 文本文件处理 - 统计单词
            System.out.println("\n9. 文本文件处理 - 统计单词：");
            
            // 创建一个英文文本文件
            File englishFile = new File("chapter12/english.txt");
            
            try (PrintWriter writer = new PrintWriter(new FileWriter(englishFile))) {
                writer.println("This is a sample text file for word counting.");
                writer.println("Java IO provides classes for reading and writing text.");
                writer.println("Character streams handle text files properly.");
                writer.println("BufferedReader and BufferedWriter improve efficiency.");
                writer.println("PrintWriter offers formatted output capabilities.");
                
                System.out.println("成功创建英文文本文件: " + englishFile.getPath());
            }
            
            // 统计单词数量
            try (BufferedReader reader = new BufferedReader(new FileReader(englishFile))) {
                String line;
                int wordCount = 0;
                
                while ((line = reader.readLine()) != null) {
                    // 使用空格分割单词（简化处理，实际应用中可能需要更复杂的处理）
                    String[] words = line.split("\\s+");
                    wordCount += words.length;
                }
                
                System.out.println("英文文件单词统计：");
                System.out.println("总单词数: " + wordCount);
            }
            
            // 10. 文本处理 - 查找和替换
            System.out.println("\n10. 文本处理 - 查找和替换：");
            
            // 读取文件内容到内存
            List<String> lines = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(new FileReader(englishFile))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                }
            }
            
            // 执行替换
            String searchWord = "text";
            String replaceWord = "TEXT";
            int replaceCount = 0;
            
            List<String> modifiedLines = new ArrayList<>();
            for (String line : lines) {
                // 替换单词（简单实现，不考虑单词边界）
                String modifiedLine = line;
                while (modifiedLine.contains(searchWord)) {
                    modifiedLine = modifiedLine.replaceFirst(searchWord, replaceWord);
                    replaceCount++;
                }
                modifiedLines.add(modifiedLine);
            }
            
            // 写回替换后的内容
            File modifiedFile = new File("chapter12/english_modified.txt");
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(modifiedFile))) {
                for (String line : modifiedLines) {
                    writer.write(line);
                    writer.newLine();
                }
            }
            
            System.out.println("替换完成: 将 '" + searchWord + "' 替换为 '" + replaceWord + "'");
            System.out.println("共替换了 " + replaceCount + " 处");
            System.out.println("替换后的文件: " + modifiedFile.getPath());
            
            // 清理文件
            System.out.println("\n11. 清理测试文件：");
            List<File> filesToDelete = Arrays.asList(
                textFile, copyFile, chineseFile, englishFile, modifiedFile
            );
            
            for (File file : filesToDelete) {
                if (file.delete()) {
                    System.out.println("已删除文件: " + file.getPath());
                }
            }
            
        } catch (IOException e) {
            System.out.println("发生I/O异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 