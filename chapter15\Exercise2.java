package chapter15;

/**
 * Exercise 2: Create a holder class that holds three objects of the same type,
 * along with the methods to store and retrieve these objects.
 */

// Generic holder for three objects of the same type
class Holder3<T> {
    private T a, b, c;
    
    public Holder3(T a, T b, T c) {
        this.a = a;
        this.b = b;
        this.c = c;
    }
    
    public void setA(T a) { this.a = a; }
    public void setB(T b) { this.b = b; }
    public void setC(T c) { this.c = c; }
    
    public T getA() { return a; }
    public T getB() { return b; }
    public T getC() { return c; }
}

public class Exercise2 {
    public static void main(String[] args) {
        // Create a Holder3 for Strings
        Holder3<String> stringHolder = new Holder3<>("First", "Second", "Third");
        System.out.println("String Holder contains: " + 
            stringHolder.getA() + ", " + 
            stringHolder.getB() + ", " + 
            stringHolder.getC());
        
        // Modify the values
        stringHolder.setA("Modified First");
        stringHolder.setB("Modified Second");
        stringHolder.setC("Modified Third");
        
        System.out.println("After modification, String Holder contains: " + 
            stringHolder.getA() + ", " + 
            stringHolder.getB() + ", " + 
            stringHolder.getC());
        
        // Create a Holder3 for Integers
        Holder3<Integer> intHolder = new Holder3<>(1, 2, 3);
        System.out.println("\nInteger Holder contains: " + 
            intHolder.getA() + ", " + 
            intHolder.getB() + ", " + 
            intHolder.getC());
        
        // Modify the values
        intHolder.setA(10);
        intHolder.setB(20);
        intHolder.setC(30);
        
        System.out.println("After modification, Integer Holder contains: " + 
            intHolder.getA() + ", " + 
            intHolder.getB() + ", " + 
            intHolder.getC());
    }
} 