package chapter14;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Exercise 9: Demonstrate type inference in generic methods and with the diamond operator.
 */
public class Exercise9 {
    public static void main(String[] args) {
        // Type inference with the diamond operator
        List<String> list1 = new ArrayList<>(); // Type inferred from declaration
        list1.add("apple");
        list1.add("banana");
        System.out.println("List with diamond operator: " + list1);
        
        // Type inference with generic methods
        List<Integer> intList = Arrays.asList(1, 2, 3, 4, 5);
        List<String> stringList = Arrays.asList("one", "two", "three");
        
        // Type inferred from arguments
        System.out.println("Last integer: " + getLast(intList));
        System.out.println("Last string: " + getLast(stringList));
        
        // Type inference with multiple type parameters
        Pair<String, Integer> p1 = makePair("Age", 25); // Types inferred from arguments
        System.out.println("Pair: " + p1.getFirst() + " = " + p1.getSecond());
        
        // Using explicit type arguments when inference fails
        List<String> emptyList = Collections.<String>emptyList();
        System.out.println("Empty list: " + emptyList);
        
        // Type inference in constructor references (Java 8+)
        Factory<GenericBox<String>> factory = GenericBox::new;
        GenericBox<String> box = factory.create();
        box.set("Hello Type Inference");
        System.out.println("Box content: " + box.get());
    }
    
    // Generic method with type inference
    public static <T> T getLast(List<T> list) {
        return list.get(list.size() - 1);
    }
    
    // Generic method with multiple type parameters
    public static <K, V> Pair<K, V> makePair(K first, V second) {
        return new Pair<>(first, second);
    }
}

// For the Collections.emptyList() example
class Collections {
    public static <T> List<T> emptyList() {
        return new ArrayList<>();
    }
}

// For constructor reference example
interface Factory<T> {
    T create();
}

class GenericBox<T> {
    private T value;
    
    public GenericBox() {
        // Default constructor
    }
    
    public void set(T value) {
        this.value = value;
    }
    
    public T get() {
        return value;
    }
} 