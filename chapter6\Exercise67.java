// 练习67：创建本章中的第一个Lunch类。创建一个名为Lunch的类，它包含一个私有构造函数，并演示餐点的创建。
package chapter6;
public class Exercise67 {
    public static void main(String[] args) {
        System.out.println("练习67：带有私有构造函数的类。");
        
        // 不能直接创建Lunch对象
        // Lunch lunch = new Lunch(); // 错误：构造函数是私有的
        
        // 必须使用静态方法
        Lunch lunch = Lunch.makeLunch();
        lunch.serveLunch();
    }
}

class Lunch {
    // 私有构造函数防止直接实例化
    private Lunch() {
        System.out.println("午餐正在准备中");
    }
    
    // 用于创建实例的公共静态方法
    public static Lunch makeLunch() {
        System.out.println("调用静态方法创建Lunch");
        return new Lunch();
    }
    
    public void serveLunch() {
        System.out.println("午餐正在上桌");
    }
} 