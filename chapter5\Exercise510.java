/**
 * 练习10：创建一个具有finalize()方法的类，并在方法中打印消息。
 * 在main()中创建一个该类的对象。试解释这个程序的行为。
 */
class WithFinalize {
    private static int counter = 0;
    private int id = counter++;
    
    // 构造器
    public WithFinalize() {
        System.out.println("WithFinalize " + id + " 被创建");
    }
    
    // finalize方法
    protected void finalize() {
        System.out.println("WithFinalize " + id + " 的finalize()方法被调用");
        System.out.println("对象 " + id + " 正在被回收...");
    }
}

public class Exercise510 {
    public static void main(String[] args) {
        System.out.println("创建对象并测试finalize方法");
        
        // 创建对象
        WithFinalize wf = new WithFinalize();
        
        // 将引用设为null
        wf = null;
        
        // 请求垃圾回收
        System.out.println("请求垃圾回收...");
        System.gc();
        
        // 给垃圾回收器一些时间
        System.out.println("等待垃圾回收器工作...");
        try {
            Thread.sleep(500);  // 等待500毫秒
        } catch(InterruptedException e) {
            e.printStackTrace();
        }
        
        System.out.println("\n程序行为解释:");
        System.out.println("1. 创建了WithFinalize类的对象");
        System.out.println("2. 将引用设置为null，使对象符合垃圾回收条件");
        System.out.println("3. 调用System.gc()请求垃圾回收");
        System.out.println("4. finalize()方法可能被调用，但不保证，因为垃圾回收是由JVM控制的");
        System.out.println("5. 即使调用了System.gc()，也不能保证垃圾回收器立即运行");
        System.out.println("6. finalize()方法在Java 9以后被废弃，不推荐使用");
    }
} 