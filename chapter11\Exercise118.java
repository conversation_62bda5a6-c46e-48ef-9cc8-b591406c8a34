package chapter11;

// 练习8：实现一个简单的自定义集合类，模拟ArrayList的部分功能

import java.util.Arrays;
import java.util.Iterator;
import java.util.NoSuchElementException;

public class Exercise118 {
    public static void main(String[] args) {
        // 创建自定义ArrayList
        System.out.println("1. 创建自定义ArrayList：");
        MyArrayList<String> fruits = new MyArrayList<>();
        
        // 添加元素
        fruits.add("苹果");
        fruits.add("香蕉");
        fruits.add("橙子");
        fruits.add("葡萄");
        
        System.out.println("初始列表大小: " + fruits.size());
        System.out.print("所有元素: ");
        for (int i = 0; i < fruits.size(); i++) {
            System.out.print(fruits.get(i) + " ");
        }
        System.out.println();
        
        // 修改元素
        System.out.println("\n2. 修改元素：");
        String oldValue = fruits.set(1, "芒果");
        System.out.println("索引1处原始值: " + oldValue);
        System.out.println("修改后: " + fruits);
        
        // 插入元素
        System.out.println("\n3. 插入元素：");
        fruits.add(2, "菠萝");
        System.out.println("插入后: " + fruits);
        
        // 删除元素
        System.out.println("\n4. 删除元素：");
        String removed = fruits.remove(0);
        System.out.println("删除的元素: " + removed);
        System.out.println("删除后: " + fruits);
        
        // 查找元素
        System.out.println("\n5. 查找元素：");
        int index = fruits.indexOf("葡萄");
        System.out.println("葡萄的索引: " + index);
        System.out.println("列表是否包含'菠萝': " + fruits.contains("菠萝"));
        System.out.println("列表是否包含'西瓜': " + fruits.contains("西瓜"));
        
        // 使用迭代器遍历
        System.out.println("\n6. 使用迭代器遍历：");
        Iterator<String> iterator = fruits.iterator();
        while (iterator.hasNext()) {
            System.out.println("水果: " + iterator.next());
        }
        
        // 清空列表
        System.out.println("\n7. 清空列表：");
        fruits.clear();
        System.out.println("清空后大小: " + fruits.size());
        System.out.println("列表是否为空: " + fruits.isEmpty());
        
        // 测试扩容
        System.out.println("\n8. 测试自动扩容：");
        MyArrayList<Integer> numbers = new MyArrayList<>(2);  // 初始容量2
        System.out.println("初始容量: 2");
        
        for (int i = 1; i <= 10; i++) {
            numbers.add(i);
            System.out.println("添加元素 " + i + " 后大小: " + numbers.size() + ", 容量: " + numbers.getCapacity());
        }
        
        System.out.println("\n最终列表: " + numbers);
    }
}

// 自定义ArrayList实现
class MyArrayList<E> implements Iterable<E> {
    private static final int DEFAULT_CAPACITY = 10;
    private Object[] elementData;
    private int size;
    
    // 构造函数
    public MyArrayList() {
        this(DEFAULT_CAPACITY);
    }
    
    public MyArrayList(int initialCapacity) {
        if (initialCapacity < 0) {
            throw new IllegalArgumentException("初始容量不能为负数: " + initialCapacity);
        }
        this.elementData = new Object[initialCapacity];
        this.size = 0;
    }
    
    // 添加元素到末尾
    public boolean add(E element) {
        ensureCapacity(size + 1);
        elementData[size++] = element;
        return true;
    }
    
    // 在指定位置插入元素
    public void add(int index, E element) {
        if (index < 0 || index > size) {
            throw new IndexOutOfBoundsException("索引: " + index + ", 大小: " + size);
        }
        
        ensureCapacity(size + 1);
        
        // 将指定位置后的所有元素向后移动一位
        System.arraycopy(elementData, index, elementData, index + 1, size - index);
        elementData[index] = element;
        size++;
    }
    
    // 获取指定位置的元素
    @SuppressWarnings("unchecked")
    public E get(int index) {
        if (index < 0 || index >= size) {
            throw new IndexOutOfBoundsException("索引: " + index + ", 大小: " + size);
        }
        return (E) elementData[index];
    }
    
    // 设置指定位置的元素，并返回旧值
    @SuppressWarnings("unchecked")
    public E set(int index, E element) {
        if (index < 0 || index >= size) {
            throw new IndexOutOfBoundsException("索引: " + index + ", 大小: " + size);
        }
        
        E oldValue = (E) elementData[index];
        elementData[index] = element;
        return oldValue;
    }
    
    // 删除指定位置的元素
    @SuppressWarnings("unchecked")
    public E remove(int index) {
        if (index < 0 || index >= size) {
            throw new IndexOutOfBoundsException("索引: " + index + ", 大小: " + size);
        }
        
        E oldValue = (E) elementData[index];
        
        // 将删除位置后的元素向前移动一位
        int numMoved = size - index - 1;
        if (numMoved > 0) {
            System.arraycopy(elementData, index + 1, elementData, index, numMoved);
        }
        
        // 清空最后位置的引用并减少大小
        elementData[--size] = null;
        return oldValue;
    }
    
    // 查找元素的索引，如果不存在则返回-1
    public int indexOf(Object o) {
        if (o == null) {
            for (int i = 0; i < size; i++) {
                if (elementData[i] == null) {
                    return i;
                }
            }
        } else {
            for (int i = 0; i < size; i++) {
                if (o.equals(elementData[i])) {
                    return i;
                }
            }
        }
        return -1;
    }
    
    // 检查是否包含指定元素
    public boolean contains(Object o) {
        return indexOf(o) >= 0;
    }
    
    // 清空列表
    public void clear() {
        // 清空所有引用以便垃圾回收
        for (int i = 0; i < size; i++) {
            elementData[i] = null;
        }
        size = 0;
    }
    
    // 检查列表是否为空
    public boolean isEmpty() {
        return size == 0;
    }
    
    // 返回列表大小
    public int size() {
        return size;
    }
    
    // 获取当前容量
    public int getCapacity() {
        return elementData.length;
    }
    
    // 确保容量足够
    private void ensureCapacity(int minCapacity) {
        // 如果当前容量不足
        if (minCapacity > elementData.length) {
            // 新容量为旧容量的1.5倍
            int newCapacity = elementData.length + (elementData.length >> 1);
            
            // 如果新容量仍然不够
            if (newCapacity < minCapacity) {
                newCapacity = minCapacity;
            }
            
            // 创建新数组并复制元素
            elementData = Arrays.copyOf(elementData, newCapacity);
        }
    }
    
    // 迭代器实现
    @Override
    public Iterator<E> iterator() {
        return new MyIterator();
    }
    
    // 自定义迭代器
    private class MyIterator implements Iterator<E> {
        private int cursor = 0;
        
        @Override
        public boolean hasNext() {
            return cursor < size;
        }
        
        @SuppressWarnings("unchecked")
        @Override
        public E next() {
            if (cursor >= size) {
                throw new NoSuchElementException();
            }
            return (E) elementData[cursor++];
        }
    }
    
    // 重写toString方法
    @Override
    public String toString() {
        if (size == 0) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < size; i++) {
            sb.append(elementData[i]);
            if (i < size - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }
} 