// 练习68：按照Connections.java示例的形式，创建一个名为ConnectionManager的类，
// 管理一个固定大小的Connection对象数组。客户端程序员不能显式创建Connection对象，
// 只能通过ConnectionManager中的静态方法获取它们。当ConnectionManager用完对象时，
// 返回null引用。在main()中测试这些类。

public class Exercise68 {
    public static void main(String[] args) {
        System.out.println("练习68：具有固定连接数的连接管理器。");
        
        // 获取连接直到用完
        Connection conn;
        int count = 1;
        
        while((conn = ConnectionManager.getConnection()) != null) {
            System.out.println("获得连接 " + count++);
            conn.doSomething();
        }
        
        System.out.println("没有更多可用连接");
        
        // 不能直接创建Connection
        // Connection c = new Connection(); // 错误：构造函数不可见
    }
}

// 具有包级私有构造函数的Connection类
class Connection {
    // 私有构造函数，只有ConnectionManager可以创建
    private static int counter = 0;
    private int id = counter++;
    
    // 包访问构造函数（不是public）
    Connection() { }
    
    public void doSomething() {
        System.out.println("连接 " + id + " 正在工作");
    }
}

// 连接管理器类
class ConnectionManager {
    // 连接数组（固定大小）
    private static final int MAX_CONNECTIONS = 3;
    private static Connection[] connections = new Connection[MAX_CONNECTIONS];
    private static int nextAvailable = 0;
    
    // 初始化连接
    static {
        for(int i = 0; i < MAX_CONNECTIONS; i++) {
            connections[i] = new Connection();
        }
    }
    
    // 获取连接的公共方法
    public static Connection getConnection() {
        if(nextAvailable < MAX_CONNECTIONS) {
            return connections[nextAvailable++];
        } else {
            return null; // 没有更多可用连接
        }
    }

    // 防止实例化
    private ConnectionManager() { }
} 