// 练习11：修改Detergent.java，使它使用委托。

package chapter7;

class Cleanser2 {
    private String s = "Cleanser";
    
    public void append(String a) {
        s += a;
    }
    
    public void dilute() {
        append(" dilute()");
    }
    
    public void apply() {
        append(" apply()");
    }
    
    public void scrub() {
        append(" scrub()");
    }
    
    public String toString() {
        return s;
    }
}

public class Exercise711 {
    private Cleanser2 cleanser = new Cleanser2();
    
    // Delegation methods:
    public void append(String a) {
        cleanser.append(a);
    }
    
    public void dilute() {
        cleanser.dilute();
    }
    
    public void apply() {
        cleanser.apply();
    }
    
    public void scrub() {
        cleanser.scrub();
        append(" Exercise711.scrub()");
    }
    
    public void sterilize() {
        append(" sterilize()");
    }
    
    public String toString() {
        return cleanser.toString();
    }
    
    public static void main(String[] args) {
        Exercise711 x = new Exercise711();
        x.dilute();
        x.apply();
        x.scrub();
        x.sterilize();
        System.out.println(x);
    }
} 