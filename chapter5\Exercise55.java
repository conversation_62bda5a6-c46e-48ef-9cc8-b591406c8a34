/**
 * 练习5：创建一个名为Dog的类，它具有重载的bark()方法。
 * 此方法应根据不同的基本数据类型进行重载，并根据调用的版本，
 * 打印出不同类型的狗吠（barking）、咆哮（howling）等信息。
 */
class Dog5 {
    void bark() {
        System.out.println("默认的狗吠声");
    }
    
    void bark(int count) {
        System.out.println("狗吠了" + count + "次: 汪汪汪!");
    }
    
    void bark(double pitch) {
        if (pitch < 1.0)
            System.out.println("低沉的狗吠声: 呜呜...");
        else if (pitch > 2.0)
            System.out.println("高声的狗吠声: 汪汪汪!!!");
        else
            System.out.println("正常的狗吠声: 汪汪!");
    }
    
    void bark(char mood) {
        switch(mood) {
            case 'A':
                System.out.println("生气的咆哮: 呜呜呜呜呜!!!");
                break;
            case 'H':
                System.out.println("开心的吠叫: 汪汪汪!");
                break;
            case 'S':
                System.out.println("悲伤的嚎叫: 嗷呜...");
                break;
            default:
                System.out.println("平静的狗吠声");
        }
    }
    
    void bark(boolean hungry) {
        if(hungry)
            System.out.println("饥饿的哀嚎: 呜呜呜...");
        else
            System.out.println("满足的低吠: 呼噜呼噜...");
    }
}

public class Exercise55 {
    public static void main(String[] args) {
        Dog5 dog = new Dog5();
        dog.bark();
        dog.bark(3);
        dog.bark(0.5);
        dog.bark(3.0);
        dog.bark('A');
        dog.bark('H');
        dog.bark(true);
        dog.bark(false);
    }
} 