import static net.mindview.util.Print.print;
public class Exercise45 {
	public static void main(String[] args) {
		int i = 1;
		int j = 2;

		// 自定义方法将整数转换为二进制字符串，结合三元操作符
		String binaryI = toBinaryString(i);
		String binaryJ = toBinaryString(j);
		String binaryAnd = toBinaryString(i & j);
		String binaryOr = toBinaryString(i | j);
		String binaryXor = toBinaryString(i ^ j);
		String binaryNotI = toBinaryString(~i);
		String binaryNotJ = toBinaryString(~j);

		print("i = " + binaryI);
		print("j = " + binaryJ);
		print("i & j = " + binaryAnd);
		print("i | j = " + binaryOr);
		print("i ^ j = " + binaryXor);
		print("~i = " + binaryNotI);
		print("~j = " + binaryNotJ);
	}

	// 自定义方法，使用三元操作符将整数转换为二进制字符串
	public static String toBinaryString(int num) {
		StringBuilder binary = new StringBuilder();
		for (int i = 31; i >= 0; i--) {
			binary.append((num & (1 << i)) != 0 ? "1" : "0");
		}
		return binary.toString();
	}
}