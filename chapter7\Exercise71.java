// 练习1：创建一个简单的类。在第二个类中，将一个引用定义为第一个类的对象。
// 运用惰性初始化来实例化这个对象。

package chapter7;

class Simple {
    Simple() {
        System.out.println("Simple constructor");
    }
}

public class Exercise71 {
    private Simple simple;
    
    public Simple getSimple() {
        if(simple == null){
            simple = new Simple();
        }
        return simple;
    }
    
    public static void main(String[] args) {
        Exercise71 e = new Exercise71();
        System.out.println("Calling getSimple() first time:");
        e.getSimple();
        System.out.println("Calling getSimple() second time:");
        e.getSimple();
    }
} 