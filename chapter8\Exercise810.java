// 练习10：创建一个基类包含protected数据，并通过组合添加一个引用该类型的数据成员。
// 派生一个类并在其构造器中初始化基类的protected成员。


class BaseWithProtected {
    protected int data;
    
    public BaseWithProtected(int data) {
        this.data = data;
    }
    
    public String toString() {
        return "BaseWithProtected data = " + data;
    }
}

class Composite {
    private BaseWithProtected base;
    
    public Composite(BaseWithProtected base) {
        this.base = base;
    }
    
    public String toString() {
        return "Composite containing: " + base;
    }
}

class Derived extends BaseWithProtected {
    private int derivedData;

    public Derived(int baseData, int derivedData) {
        super(baseData);
        this.derivedData = derivedData;
        // Access to protected member of base class:
        this.data = baseData * 2; // Modify the protected data
    }
    
    @Override
    public String toString() {
        return "Derived: base data = " + data + 
               ", derived data = " + derivedData;
    }
}

public class Exercise810 {
    public static void main(String[] args) {
        BaseWithProtected base = new BaseWithProtected(10);
        System.out.println(base);
        
        Composite comp = new Composite(base);
        System.out.println(comp);
        
        Derived derived = new Derived(20, 30);
        System.out.println(derived);
        
        // Demonstrate composition with a derived object
        Composite comp2 = new Composite(derived);
        System.out.println(comp2);
    }
} 