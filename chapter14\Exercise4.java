package chapter14;

/**
 * Exercise 4: Create a generic method that takes two generic arguments of different types
 * and returns a pair object containing both arguments.
 */
public class Exercise4 {
    public static void main(String[] args) {
        // Test with different types
        Pair<String, Integer> p1 = makePair("Age", 30);
        System.out.println(p1.getFirst() + ": " + p1.getSecond());
        
        Pair<Double, String> p2 = makePair(3.14, "PI");
        System.out.println(p2.getFirst() + " is " + p2.getSecond());
        
        Pair<<PERSON><PERSON>an, Character> p3 = makePair(true, 'Y');
        System.out.println("Value: " + p3.getFirst() + ", Symbol: " + p3.getSecond());
        
        // Using explicit type arguments
        Pair<Integer, Integer> p4 = GenericMethods.<Integer, Integer>makePair(10, 20);
        System.out.println("First: " + p4.getFirst() + ", Second: " + p4.getSecond());
    }
    
    // Generic method using the Pair class from Exercise3
    public static <T, U> Pair<T, U> makePair(T first, U second) {
        return new Pair<>(first, second);
    }
}

// If Pair class is not accessible from Exercise3, uncomment this code:
/*
class Pair<A, B> {
    private A first;
    private B second;
    
    public Pair(A first, B second) {
        this.first = first;
        this.second = second;
    }
    
    public A getFirst() { return first; }
    public B getSecond() { return second; }
    
    public void setFirst(A first) { this.first = first; }
    public void setSecond(B second) { this.second = second; }
}
*/

class GenericMethods {
    public static <T, U> Pair<T, U> makePair(T first, U second) {
        return new Pair<>(first, second);
    }
} 