package chapter13;

/**
 * Exercise 13.3: Thread Coordination with wait(), notify(), and notifyAll()
 * 
 * This exercise demonstrates how threads can coordinate using:
 * 1. wait() - causes a thread to wait until another thread invokes notify() or notifyAll()
 * 2. notify() - wakes up a single thread that is waiting on this object's monitor
 * 3. notifyAll() - wakes up all threads that are waiting on this object's monitor
 */
public class Exercise133 {
    
    public static void main(String[] args) {
        System.out.println("==== Thread Coordination with wait/notify ====");
        
        // 1. Producer-Consumer problem with wait/notify
        System.out.println("\n1. Producer-Consumer Example:");
        SharedQueue sharedQueue = new SharedQueue(5); // Queue with capacity 5
        
        Thread producerThread = new Thread(() -> {
            try {
                for (int i = 1; i <= 10; i++) {
                    sharedQueue.produce(i);
                    Thread.sleep(300); // Slow down producer for demonstration
                }
            } catch (InterruptedException e) {
                System.out.println("Producer interrupted");
            }
        }, "Producer-Thread");
        
        Thread consumerThread = new Thread(() -> {
            try {
                for (int i = 1; i <= 10; i++) {
                    sharedQueue.consume();
                    Thread.sleep(500); // Consumer is slower than producer
                }
            } catch (InterruptedException e) {
                System.out.println("Consumer interrupted");
            }
        }, "Consumer-Thread");
        
        producerThread.start();
        consumerThread.start();
        
        try {
            producerThread.join();
            consumerThread.join();
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        // 2. Multiple consumers with notifyAll()
        System.out.println("\n2. Multiple Consumers with notifyAll():");
        SharedQueue multiConsumerQueue = new SharedQueue(3); // Queue with capacity 3
        
        Thread multiProducerThread = new Thread(() -> {
            try {
                for (int i = 1; i <= 8; i++) {
                    multiConsumerQueue.produce(i * 10);
                    Thread.sleep(400);
                }
            } catch (InterruptedException e) {
                System.out.println("Multi-producer interrupted");
            }
        }, "Multi-Producer");
        
        // Create multiple consumer threads
        Thread[] consumerThreads = new Thread[3];
        for (int i = 0; i < consumerThreads.length; i++) {
            final int consumerId = i + 1;
            consumerThreads[i] = new Thread(() -> {
                try {
                    for (int j = 1; j <= 3; j++) {
                        int item = multiConsumerQueue.consume();
                        System.out.println("Consumer-" + consumerId + " consumed: " + item);
                        Thread.sleep(500 + (consumerId * 100)); // Different speeds
                    }
                } catch (InterruptedException e) {
                    System.out.println("Consumer-" + consumerId + " interrupted");
                }
            }, "Consumer-" + consumerId);
        }
        
        multiProducerThread.start();
        for (Thread consumer : consumerThreads) {
            consumer.start();
        }
        
        try {
            multiProducerThread.join();
            for (Thread consumer : consumerThreads) {
                consumer.join();
            }
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        // 3. Thread signaling with wait/notify
        System.out.println("\n3. Thread Signaling Example:");
        ThreadSignaler signaler = new ThreadSignaler();
        
        Thread waiterThread = new Thread(() -> {
            try {
                signaler.waitForSignal();
            } catch (InterruptedException e) {
                System.out.println("Waiter thread interrupted");
            }
        }, "Waiter-Thread");
        
        Thread notifierThread = new Thread(() -> {
            try {
                Thread.sleep(2000); // Wait for 2 seconds
                signaler.sendSignal();
            } catch (InterruptedException e) {
                System.out.println("Notifier thread interrupted");
            }
        }, "Notifier-Thread");
        
        waiterThread.start();
        notifierThread.start();
        
        try {
            waiterThread.join();
            notifierThread.join();
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        System.out.println("\nAll thread coordination examples have completed!");
    }
}

// Shared queue with wait/notify for coordination
class SharedQueue {
    private final int[] buffer;
    private final int capacity;
    private int size = 0;
    private int inIndex = 0;
    private int outIndex = 0;
    
    public SharedQueue(int capacity) {
        this.capacity = capacity;
        this.buffer = new int[capacity];
    }
    
    public synchronized void produce(int item) throws InterruptedException {
        // Wait while queue is full
        while (size == capacity) {
            System.out.println(Thread.currentThread().getName() + " waiting, queue is full");
            wait(); // Release lock and wait for consumer to notify
        }
        
        buffer[inIndex] = item;
        inIndex = (inIndex + 1) % capacity; // Circular buffer
        size++;
        
        System.out.println(Thread.currentThread().getName() + " produced: " + item + ", Queue size: " + size);
        notify(); // Notify waiting consumer if any
    }
    
    public synchronized int consume() throws InterruptedException {
        // Wait while queue is empty
        while (size == 0) {
            System.out.println(Thread.currentThread().getName() + " waiting, queue is empty");
            wait(); // Release lock and wait for producer to notify
        }
        
        int item = buffer[outIndex];
        outIndex = (outIndex + 1) % capacity; // Circular buffer
        size--;
        
        System.out.println(Thread.currentThread().getName() + " consumed: " + item + ", Queue size: " + size);
        notify(); // Notify waiting producer if any
        
        return item;
    }
    
    // For the multiple consumer scenario, we need to wake all waiting threads
    public synchronized void notifyAllWaiting() {
        notifyAll();
    }
}

// Thread signaling with wait/notify
class ThreadSignaler {
    private boolean signalSent = false;
    
    public synchronized void waitForSignal() throws InterruptedException {
        System.out.println(Thread.currentThread().getName() + " waiting for signal...");
        
        while (!signalSent) {
            wait();
        }
        
        System.out.println(Thread.currentThread().getName() + " received signal and continuing execution");
    }
    
    public synchronized void sendSignal() {
        System.out.println(Thread.currentThread().getName() + " sending signal...");
        signalSent = true;
        notify();
        System.out.println(Thread.currentThread().getName() + " signal sent");
    }
} 