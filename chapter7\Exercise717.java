// 练习17：修改练习16，使Frog覆盖基类中的方法定义（也就是使用相同的方法签名和返回类型）。
// 验证在main()中，当你向上转型时，行为发生了改变。

package chapter7;

class Amphibian2 {
    public void swim() {
        System.out.println("Amphibian swimming");
    }
    
    public void walk() {
        System.out.println("Amphibian walking");
    }
    
    public void breathe() {
        System.out.println("Amphibian breathing");
    }
}

class Frog2 extends Amphibian2 {
    // Override methods from base class
    @Override
    public void swim() {
        System.out.println("Frog swimming");
    }
    
    @Override
    public void walk() {
        System.out.println("Frog walking");
    }
    
    @Override
    public void breathe() {
        System.out.println("Frog breathing");
    }
}

public class Exercise717 {
    public static void main(String[] args) {
        Frog2 frog = new Frog2();
        
        // Test methods on Frog directly
        System.out.println("Calling methods on Frog reference:");
        frog.swim();
        frog.walk();
        frog.breathe();
        
        // Upcast to Amphibian and test methods
        System.out.println("\nCalling methods on Amphibian reference:");
        Amphibian2 amphibian = frog; // Upcast
        amphibian.swim();
        amphibian.walk();
        amphibian.breathe();
    }
} 