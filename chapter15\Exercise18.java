package chapter15;

import java.util.*;

/**
 * Exercise 18: Following the form of BankTeller.java, create an example where
 * BigFish eat LittleFish in the Ocean.
 */

// The Ocean where fish live
class Ocean {
    private static Random rand = new Random();
    
    // Create a pool of fish
    public static <T> void fill(Collection<T> collection, Generator<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            collection.add(gen.next());
        }
    }
}

// Generator interface
interface Generator<T> {
    T next();
}

// Base fish class
class Fish {
    private static long counter = 1;
    private final long id = counter++;
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + " " + id;
    }
}

// LittleFish get eaten by BigFish
class LittleFish extends Fish {
    private static int counter = 1;
    private final int id = counter++;
    
    private LittleFish() {} // Private constructor
    
    // Generator for LittleFish
    public static Generator<LittleFish> generator() {
        return new Generator<LittleFish>() {
            @Override
            public LittleFish next() {
                return new LittleFish();
            }
        };
    }
}

// BigFish eat LittleFish
class BigFish extends Fish {
    private static int counter = 1;
    private final int id = counter++;
    private LittleFish lunch;
    
    private BigFish() {} // Private constructor
    
    // BigFish eat LittleFish
    public void eat(LittleFish littleFish) {
        lunch = littleFish;
    }
    
    @Override
    public String toString() {
        return super.toString() + " has eaten " + 
            (lunch == null ? "nothing yet" : lunch);
    }
    
    // Generator for BigFish
    public static Generator<BigFish> generator() {
        return new Generator<BigFish>() {
            @Override
            public BigFish next() {
                return new BigFish();
            }
        };
    }
}

public class Exercise18 {
    public static void serve(BigFish bigFish, LittleFish littleFish) {
        bigFish.eat(littleFish);
    }
    
    public static void main(String[] args) {
        Random rand = new Random();
        
        // Create lists of fish
        List<LittleFish> littleFishList = new ArrayList<>();
        Ocean.fill(littleFishList, LittleFish.generator(), 15);
        
        List<BigFish> bigFishList = new ArrayList<>();
        Ocean.fill(bigFishList, BigFish.generator(), 5);
        
        System.out.println("LittleFish in the ocean:");
        for(LittleFish fish : littleFishList) {
            System.out.println(fish);
        }
        
        System.out.println("\nBigFish in the ocean:");
        for(BigFish fish : bigFishList) {
            System.out.println(fish);
        }
        
        // Let the feeding begin
        System.out.println("\nFeeding time in the ocean:");
        for(BigFish bigFish : bigFishList) {
            // Check if there are still little fish to eat
            if(littleFishList.size() == 0) {
                System.out.println("No more LittleFish to eat!");
                break;
            }
            
            // Select a random LittleFish to eat
            int fishIndex = rand.nextInt(littleFishList.size());
            LittleFish lunch = littleFishList.remove(fishIndex);
            serve(bigFish, lunch);
            System.out.println(bigFish);
        }
        
        // Show remaining LittleFish
        System.out.println("\nRemaining LittleFish in the ocean: " + 
            littleFishList.size());
        for(LittleFish fish : littleFishList) {
            System.out.println(fish);
        }
    }
}