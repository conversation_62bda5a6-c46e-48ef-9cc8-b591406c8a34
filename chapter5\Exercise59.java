/**
 * 练习9：编写具有两个（重载）构造器的类，并在第一个构造器中通过this调用第二个构造器。
 */
class TwoConstructors {
    private String name;
    private int id;
    
    // 第一个构造器，只接收name参数，通过this调用第二个构造器
    TwoConstructors(String name) {
        // 通过this调用第二个构造器，设置默认id为0
        this(name, 0);
        System.out.println("第一个构造器执行完毕，name = " + this.name + ", id = " + this.id);
    }
    
    // 第二个构造器，接收name和id两个参数
    TwoConstructors(String name, int id) {
        this.name = name;
        this.id = id;
        System.out.println("第二个构造器执行完毕，name = " + this.name + ", id = " + this.id);
    }
    
    void printInfo() {
        System.out.println("对象信息 - 名称: " + name + ", ID: " + id);
    }
}

public class Exercise59 {
    public static void main(String[] args) {
        System.out.println("创建第一个对象，只传递name参数:");
        TwoConstructors tc1 = new TwoConstructors("对象一");
        tc1.printInfo();
        
        System.out.println("\n创建第二个对象，传递name和id参数:");
        TwoConstructors tc2 = new TwoConstructors("对象二", 100);
        tc2.printInfo();
    }
} 