package chapter16;

import java.util.*;

/**
 * Exercise 2: Write a method that takes an int argument and returns an array of
 * that size, filled with BerylliumSphere objects.
 */

// Using BerylliumSphere class from Exercise 1
class BerylliumSphere2 {
    private static long counter;
    private final long id = counter++;
    
    @Override
    public String toString() {
        return "Sphere " + id;
    }
}

public class Exercise2 {
    // Method that creates and returns an array of BerylliumSphere objects
    public static BerylliumSphere2[] createBerylliumSphereArray(int size) {
        if (size < 0) {
            throw new IllegalArgumentException("Array size cannot be negative");
        }
        
        BerylliumSphere2[] result = new BerylliumSphere2[size];
        
        // Fill the array with BerylliumSphere objects
        for(int i = 0; i < size; i++) {
            result[i] = new BerylliumSphere2();
        }
        
        return result;
    }
    
    public static void main(String[] args) {
        // Test with different sizes
        System.out.println("Empty array (size 0):");
        BerylliumSphere2[] array1 = createBerylliumSphereArray(0);
        System.out.println(Arrays.toString(array1));
        
        System.out.println("\nSmall array (size 5):");
        BerylliumSphere2[] array2 = createBerylliumSphereArray(5);
        System.out.println(Arrays.toString(array2));
        
        System.out.println("\nLarger array (size 10):");
        BerylliumSphere2[] array3 = createBerylliumSphereArray(10);
        System.out.println(Arrays.toString(array3));
        
        // Test with error condition
        try {
            System.out.println("\nInvalid size (-1):");
            BerylliumSphere2[] array4 = createBerylliumSphereArray(-1);
            System.out.println(Arrays.toString(array4)); // Should not reach here
        } catch(IllegalArgumentException e) {
            System.out.println("Caught exception: " + e.getMessage());
        }
        
        // Another approach using Stream API (Java 8+)
        System.out.println("\nUsing Stream API (size 3):");
        BerylliumSphere2[] array5 = new Random().ints(3, 0, 100)
            .mapToObj(i -> new BerylliumSphere2())
            .toArray(BerylliumSphere2[]::new);
        System.out.println(Arrays.toString(array5));
    }
} 