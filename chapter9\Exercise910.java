// Exercise 10: Modify Music5.java by adding a Playable interface. Move the play() 
// declaration from Instrument to Playable. Add Playable to the derived classes 
// by including it in the implements list. Change tune() so that it takes a 
// Playable instead of an Instrument.

interface Playable {
    void play();
}

abstract class Instrument10 {
    // play() moved to Playable interface
    public abstract String what();
    public abstract void adjust();
}

class Wind10 extends Instrument10 implements Playable {
    @Override
    public void play() {
        System.out.println("Wind10.play()");
    }
    
    @Override
    public String what() {
        return "Wind10";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Wind10");
    }
}

class Percussion10 extends Instrument10 implements Playable {
    @Override
    public void play() {
        System.out.println("Percussion10.play()");
    }
    
    @Override
    public String what() {
        return "Percussion10";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Percussion10");
    }
}

class Stringed10 extends Instrument10 implements Playable {
    @Override
    public void play() {
        System.out.println("Stringed10.play()");
    }
    
    @Override
    public String what() {
        return "Stringed10";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Stringed10");
    }
}

class Brass10 extends Wind10 {
    @Override
    public void play() {
        System.out.println("Brass10.play()");
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Brass10");
    }
}

class Woodwind10 extends Wind10 {
    @Override
    public void play() {
        System.out.println("Woodwind10.play()");
    }
    
    @Override
    public String what() {
        return "Woodwind10";
    }
}

public class Exercise910 {
    // Modified to accept Playable instead of Instrument
    static void tune(Playable p) {
        p.play();
    }
    
    // Modified to accept Playable[] instead of Instrument[]
    static void tuneAll(Playable[] e) {
        for(Playable p : e)
            tune(p);
    }
    
    public static void main(String[] args) {
        // Note that we still use Instrument10 types in the array,
        // but they all implement Playable
        Playable[] orchestra = {
            new Wind10(),
            new Percussion10(),
            new Stringed10(),
            new Brass10(),
            new Woodwind10()
        };
        tuneAll(orchestra);
        
        System.out.println("\nExercise 10 completed successfully");
    }
} 