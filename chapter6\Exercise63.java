// 练习63：创建一个具有public、private、protected和包访问权限的字段和方法的类。创建这个类的对象，并尝试访问所有类成员，看看会得到什么样的编译器错误。

public class Exercise63 {
    public static void main(String[] args) {
        System.out.println("练习63：创建一个具有public、private、protected和包访问权限的字段和方法的类。");
        
        AccessLevels obj = new AccessLevels();
        
        // 可访问的成员
        obj.publicField = 10;
        obj.publicMethod();
        obj.packageField = 20;
        obj.packageMethod();
        obj.protectedField = 30;
        obj.protectedMethod();
        
        // 不可直接访问的成员
        obj.protectedField = 30; // 只能在子类或同一包中访问
        obj.protectedMethod(); // 只能在子类或同一包中访问
        //obj.privateField = 40; // 不能在类外访问
        //obj.privateMethod(); // 不能在类外访问
    }
}

class AccessLevels {
    public int publicField;
    protected int protectedField;
    int packageField; // 包访问权限（无修饰符）
    private int privateField;
    
    public void publicMethod() {
        System.out.println("公共方法");
    }
    
    protected void protectedMethod() {
        System.out.println("受保护方法");
    }
    
    void packageMethod() {
        System.out.println("包访问方法");
    }
    
    private void privateMethod() {
        System.out.println("私有方法");
    }
} 