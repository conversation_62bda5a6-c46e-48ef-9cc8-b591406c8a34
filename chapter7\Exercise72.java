// 练习2：从Detergent中继承产生一个新的类。覆盖scrub()
// 并添加一个名为sterilize()的新方法。

package chapter7;

class Cleanser {
    private String s = "Cleanser ";
    public void append(String a) {
        s += a;
    }

    public void dilute() {
        append("dilute() ");
    }

    public void apply() {
        append("apply() ");
    }

    public void scrub() {
        append("scrub() ");
    }

    public String toString() {
        return s;
    }
}

class Detergent extends Cleanser {
    @Override
    public void scrub() {
        append("Detergent.scrub()");
        super.scrub();
    }

    public void foam() {
        append("foam()");
    }
}

public class Exercise72 extends Detergent {
    // Override
    public void scrub() {
        append(" Exercise72.scrub()");
    }
    
    // Add a new method:
    public void sterilize() {
        append(" sterilize()");
    }
    
    public static void main(String[] args) {
        Exercise72 x = new Exercise72();
        x.dilute();
        x.apply();
        x.scrub();
        x.sterilize();
        System.out.println(x);
    }
} 