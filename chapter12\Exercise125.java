package chapter12;

// 练习5：使用对象序列化将对象保存到文件并从文件中恢复对象

import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Exercise125 {
    public static void main(String[] args) {
        // 定义文件路径
        File serializedFile = new File("chapter12/serialized_objects.dat");
        
        try {
            // 确保目录存在
            serializedFile.getParentFile().mkdirs();
            
            // 1. 创建要序列化的对象
            System.out.println("1. 创建要序列化的对象：");
            
            Person person1 = new Person("张三", 25, "Beijing");
            Person person2 = new Person("李四", 30, "Shanghai");
            Student student = new Student("王五", 20, "Guangzhou", "Computer Science", 3.8);
            
            System.out.println("创建了三个对象:");
            System.out.println(person1);
            System.out.println(person2);
            System.out.println(student);
            
            // 2. 将对象序列化到文件
            System.out.println("\n2. 将对象序列化到文件：");
            serializeObjects(serializedFile, person1, person2, student);
            System.out.println("对象已序列化到文件: " + serializedFile.getPath());
            
            // 3. 从文件反序列化对象
            System.out.println("\n3. 从文件反序列化对象：");
            List<Object> deserializedObjects = deserializeObjects(serializedFile);
            
            System.out.println("反序列化得到 " + deserializedObjects.size() + " 个对象:");
            for (Object obj : deserializedObjects) {
                System.out.println(obj);
            }
            
            // 4. 序列化含有非序列化字段的对象
            System.out.println("\n4. 序列化含有非序列化字段的对象：");
            
            Employee employee = new Employee("赵六", 35, "Chengdu", "IT Manager", 15000.0);
            System.out.println("创建的Employee对象:");
            System.out.println(employee);
            
            File employeeFile = new File("chapter12/employee.dat");
            
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(employeeFile))) {
                oos.writeObject(employee);
                System.out.println("Employee对象已序列化到文件: " + employeeFile.getPath());
            }
            
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(employeeFile))) {
                Employee deserializedEmployee = (Employee) ois.readObject();
                System.out.println("反序列化后的Employee对象:");
                System.out.println(deserializedEmployee);
                System.out.println("注意：由于salary字段被标记为transient，其值在反序列化后为0.0");
            }
            
            // 5. 序列化列表
            System.out.println("\n5. 序列化列表：");
            
            List<Person> personList = new ArrayList<>();
            personList.add(new Person("刘一", 22, "Wuhan"));
            personList.add(new Person("陈二", 27, "Nanjing"));
            personList.add(new Person("张三", 31, "Tianjin"));
            
            File listFile = new File("chapter12/person_list.dat");
            
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(listFile))) {
                oos.writeObject(personList);
                System.out.println("Person列表已序列化到文件: " + listFile.getPath());
            }
            
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(listFile))) {
                @SuppressWarnings("unchecked")
                List<Person> deserializedList = (List<Person>) ois.readObject();
                System.out.println("反序列化后的Person列表:");
                for (Person p : deserializedList) {
                    System.out.println(p);
                }
            }
            
            // 6. 序列化对象的版本控制
            System.out.println("\n6. 序列化对象的版本控制：");
            
            VersionedPerson v1 = new VersionedPerson("钱七", 40);
            File versionFile = new File("chapter12/versioned_person.dat");
            
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(versionFile))) {
                oos.writeObject(v1);
                System.out.println("VersionedPerson对象已序列化到文件: " + versionFile.getPath());
            }
            
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(versionFile))) {
                VersionedPerson deserializedV1 = (VersionedPerson) ois.readObject();
                System.out.println("反序列化后的VersionedPerson对象:");
                System.out.println(deserializedV1);
            }
            
            // 7. 自定义序列化
            System.out.println("\n7. 自定义序列化：");
            
            Date now = new Date();
            CustomSerializedClass custom = new CustomSerializedClass("Test Object", 100, now);
            System.out.println("创建的自定义序列化对象:");
            System.out.println(custom);
            
            File customFile = new File("chapter12/custom_serialized.dat");
            
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(customFile))) {
                oos.writeObject(custom);
                System.out.println("自定义序列化对象已写入文件: " + customFile.getPath());
            }
            
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(customFile))) {
                CustomSerializedClass deserializedCustom = (CustomSerializedClass) ois.readObject();
                System.out.println("反序列化后的自定义序列化对象:");
                System.out.println(deserializedCustom);
            }
            
            // 8. 清理测试文件
            System.out.println("\n8. 清理测试文件：");
            deleteFiles(serializedFile, employeeFile, listFile, versionFile, customFile);
            System.out.println("已删除所有测试文件");
            
        } catch (IOException | ClassNotFoundException e) {
            System.out.println("发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 将多个对象序列化到文件
    private static void serializeObjects(File file, Object... objects) throws IOException {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(file))) {
            for (Object obj : objects) {
                oos.writeObject(obj);
            }
        }
    }
    
    // 从文件中反序列化对象
    private static List<Object> deserializeObjects(File file) throws IOException, ClassNotFoundException {
        List<Object> objects = new ArrayList<>();
        
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file))) {
            while (true) {
                try {
                    Object obj = ois.readObject();
                    objects.add(obj);
                } catch (EOFException e) {
                    // 到达文件末尾，结束循环
                    break;
                }
            }
        }
        
        return objects;
    }
    
    // 删除多个文件
    private static void deleteFiles(File... files) {
        for (File file : files) {
            if (file.exists() && file.delete()) {
                System.out.println("已删除文件: " + file.getPath());
            }
        }
    }
}

// 可序列化的Person类
class Person implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String name;
    private int age;
    private String address;
    
    public Person(String name, int age, String address) {
        this.name = name;
        this.age = age;
        this.address = address;
    }
    
    @Override
    public String toString() {
        return "Person [name=" + name + ", age=" + age + ", address=" + address + "]";
    }
}

// 继承自Person的Student类
class Student extends Person {
    private static final long serialVersionUID = 1L;
    
    private String major;
    private double gpa;
    
    public Student(String name, int age, String address, String major, double gpa) {
        super(name, age, address);
        this.major = major;
        this.gpa = gpa;
    }
    
    @Override
    public String toString() {
        return super.toString() + " Student [major=" + major + ", gpa=" + gpa + "]";
    }
}

// 包含非序列化字段的类
class Employee extends Person {
    private static final long serialVersionUID = 1L;
    
    private String position;
    private transient double salary; // transient字段不会被序列化
    
    public Employee(String name, int age, String address, String position, double salary) {
        super(name, age, address);
        this.position = position;
        this.salary = salary;
    }
    
    @Override
    public String toString() {
        return super.toString() + " Employee [position=" + position + ", salary=" + salary + "]";
    }
}

// 使用serialVersionUID的版本控制类
class VersionedPerson implements Serializable {
    // 指定serialVersionUID，即使类结构变化，只要这个ID不变，就能兼容旧数据
    private static final long serialVersionUID = 42L;
    
    private String name;
    private int age;
    // 假设这是后来添加的字段，由于serialVersionUID不变，可以兼容旧数据
    private String email = "<EMAIL>";
    
    public VersionedPerson(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    @Override
    public String toString() {
        return "VersionedPerson [name=" + name + ", age=" + age + ", email=" + email + "]";
    }
}

// 自定义序列化过程的类
class CustomSerializedClass implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String name;
    private int value;
    private Date creationDate;
    
    public CustomSerializedClass(String name, int value, Date creationDate) {
        this.name = name;
        this.value = value;
        this.creationDate = creationDate;
    }
    
    // 自定义写入方法
    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        // 调用默认的序列化机制
        out.defaultWriteObject();
        
        // 自定义序列化逻辑
        // 例如，将Date转换为long类型的时间戳
        out.writeLong(creationDate.getTime());
        
        System.out.println("CustomSerializedClass.writeObject 被调用");
    }
    
    // 自定义读取方法
    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        // 调用默认的反序列化机制
        in.defaultReadObject();
        
        // 自定义反序列化逻辑
        // 从long类型的时间戳恢复Date对象
        long timestamp = in.readLong();
        creationDate = new Date(timestamp);
        
        System.out.println("CustomSerializedClass.readObject 被调用");
    }
    
    @Override
    public String toString() {
        return "CustomSerializedClass [name=" + name + ", value=" + value + ", creationDate=" + creationDate + "]";
    }
} 