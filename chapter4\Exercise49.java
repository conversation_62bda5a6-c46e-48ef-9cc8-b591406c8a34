public class Exercise49 {
    public static void main(String[] args) {
        for(int i = 1; i < 6; i++) {
            System.out.println(fibonacci(i));
        }

    }
    public static int fibonacci(int n) {
        if(n == 0){
            return 0;
        }else if(n == 1){
            return 1;
        }else if(n == 2){
            return 1;
        }else{
            return fibonacci(n-1) + fi<PERSON><PERSON><PERSON>(n-2);
        }

    }
}
