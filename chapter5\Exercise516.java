/**
 * 练习16：创建一个String对象数组，并用for循环来赋值数组中的各个元素。打印该数组。
 */
public class Exercise516 {
    public static void main(String[] args) {
        // 创建一个String对象的数组
        String[] stringArray = new String[10];
        
        // 用for循环给数组元素赋值
        for(int i = 0; i < stringArray.length; i++) {
            stringArray[i] = "字符串 #" + i;
        }
        
        // 打印数组
        System.out.println("数组内容:");
        for(int i = 0; i < stringArray.length; i++) {
            System.out.println("stringArray[" + i + "] = " + stringArray[i]);
        }
        
        // 和练习15对比
        System.out.println("\n本练习与练习15的区别：");
        System.out.println("1. 练习15是手动一个一个赋值");
        System.out.println("2. 本练习用for循环批量赋值");
        System.out.println("3. 当数组元素较多时，使用循环赋值更为方便");
    }
} 