// 练习64：证明protected方法有包访问权限但不是public的。

public class Exercise64 {
    public static void main(String[] args) {
        System.out.println("练习64：证明protected方法有包访问权限但不是public的。");
        
        ProtectedTest test = new ProtectedTest();
        test.useProtected(); // 在同一包内可访问
    }
}

class ProtectedTest {
    protected void protectedMethod() {
        System.out.println("这是一个protected方法");
    }
    
    public void useProtected() {
        protectedMethod(); // 可以在同一类中访问protected方法
        
        OtherClass other = new OtherClass();
        other.protectedMethod(); // 可以访问同一包中另一个类的protected方法
    }
}

class OtherClass {
    protected void protectedMethod() {
        System.out.println("来自另一个类的protected方法");
    }
} 