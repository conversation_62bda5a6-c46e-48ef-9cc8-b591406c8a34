<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/chapter2" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter3" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lib" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/chapter4" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter5" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/access" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter6" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/p1" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/p2" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter10" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter7" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter8" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter9" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter11" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/chapter12" isTestSource="false" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module-library" exported="">
      <library>
        <CLASSES>
          <root url="jar://$MODULE_DIR$/lib/net.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
  </component>
</module>