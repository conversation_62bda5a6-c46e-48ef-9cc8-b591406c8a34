package chapter15;

import java.util.*;

/**
 * Exercise 16: Add a SixTuple to Tuple.java, and test it in TupleTest.java.
 */

// Base TwoTuple class
class TwoTuple16<A, B> {
    public final A first;
    public final B second;
    
    public TwoTuple16(A a, B b) {
        first = a;
        second = b;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ")";
    }
}

// ThreeTuple extending TwoTuple
class ThreeTuple16<A, B, C> extends TwoTuple16<A, B> {
    public final C third;
    
    public ThreeTuple16(A a, B b, C c) {
        super(a, b);
        third = c;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ")";
    }
}

// FourTuple extending ThreeTuple
class FourTuple16<A, B, C, D> extends ThreeTuple16<A, B, C> {
    public final D fourth;
    
    public FourTuple16(A a, B b, C c, D d) {
        super(a, b, c);
        fourth = d;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + fourth + ")";
    }
}

// FiveTuple extending FourTuple
class FiveTuple16<A, B, C, D, E> extends FourTuple16<A, B, C, D> {
    public final E fifth;
    
    public FiveTuple16(A a, B b, C c, D d, E e) {
        super(a, b, c, d);
        fifth = e;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + fourth + ", " + fifth + ")";
    }
}

// SixTuple extending FiveTuple (the new class for this exercise)
class SixTuple16<A, B, C, D, E, F> extends FiveTuple16<A, B, C, D, E> {
    public final F sixth;
    
    public SixTuple16(A a, B b, C c, D d, E e, F f) {
        super(a, b, c, d, e);
        sixth = f;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + 
               fourth + ", " + fifth + ", " + sixth + ")";
    }
}

// Tuple utility class for creating tuples
class Tuple {
    public static <A, B> TwoTuple16<A, B> tuple(A a, B b) {
        return new TwoTuple16<A, B>(a, b);
    }
    
    public static <A, B, C> ThreeTuple16<A, B, C> tuple(A a, B b, C c) {
        return new ThreeTuple16<A, B, C>(a, b, c);
    }
    
    public static <A, B, C, D> FourTuple16<A, B, C, D> tuple(A a, B b, C c, D d) {
        return new FourTuple16<A, B, C, D>(a, b, c, d);
    }
    
    public static <A, B, C, D, E> FiveTuple16<A, B, C, D, E> tuple(A a, B b, C c, D d, E e) {
        return new FiveTuple16<A, B, C, D, E>(a, b, c, d, e);
    }
    
    // Added method for SixTuple
    public static <A, B, C, D, E, F> SixTuple16<A, B, C, D, E, F> 
    tuple(A a, B b, C c, D d, E e, F f) {
        return new SixTuple16<A, B, C, D, E, F>(a, b, c, d, e, f);
    }
}

public class Exercise16 {
    public static void main(String[] args) {
        // Test with TwoTuple
        TwoTuple16<String, Integer> tt = Tuple.tuple("String", 11);
        System.out.println("TwoTuple: " + tt);
        
        // Test with ThreeTuple
        ThreeTuple16<String, Integer, Double> tht = 
            Tuple.tuple("String", 11, 2.2);
        System.out.println("ThreeTuple: " + tht);
        
        // Test with FourTuple
        FourTuple16<String, Integer, Double, Character> ft = 
            Tuple.tuple("String", 11, 2.2, 'c');
        System.out.println("FourTuple: " + ft);
        
        // Test with FiveTuple
        FiveTuple16<String, Integer, Double, Character, Boolean> fvt = 
            Tuple.tuple("String", 11, 2.2, 'c', true);
        System.out.println("FiveTuple: " + fvt);
        
        // Test with SixTuple (new addition)
        SixTuple16<String, Integer, Double, Character, Boolean, Float> st = 
            Tuple.tuple("String", 11, 2.2, 'c', true, 3.3f);
        System.out.println("SixTuple: " + st);
        
        // Accessing individual elements
        System.out.println("\nAccessing SixTuple elements:");
        System.out.println("first: " + st.first);
        System.out.println("second: " + st.second);
        System.out.println("third: " + st.third);
        System.out.println("fourth: " + st.fourth);
        System.out.println("fifth: " + st.fifth);
        System.out.println("sixth: " + st.sixth);
    }
} 