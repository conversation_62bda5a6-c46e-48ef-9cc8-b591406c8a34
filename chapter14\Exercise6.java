package chapter14;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Exercise 6: Create a generic method that uses wildcards to print the contents
 * of different types of collections.
 */
public class Exercise6 {
    public static void main(String[] args) {
        // Create different types of collections
        List<Integer> integers = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));
        List<String> strings = new ArrayList<>(Arrays.asList("apple", "banana", "cherry"));
        List<Double> doubles = new ArrayList<>(Arrays.asList(1.1, 2.2, 3.3));
        
        // Use the generic method with wildcards
        System.out.println("Printing integers:");
        printCollection(integers);
        
        System.out.println("\nPrinting strings:");
        printCollection(strings);
        
        System.out.println("\nPrinting doubles:");
        printCollection(doubles);
        
        // Using bounded wildcards
        System.out.println("\nSum of integers: " + sumOfNumbers(integers));
        System.out.println("Sum of doubles: " + sumOfNumbers(doubles));
        
        // Using upper bounded wildcard
        List<Number> numbers = new ArrayList<>(Arrays.asList(10, 20, 30));
        System.out.println("\nNumbers before: " + numbers);
        addNumbers(numbers);
        System.out.println("Numbers after: " + numbers);
    }
    
    // Generic method with unbounded wildcard
    public static void printCollection(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
    }
    
    // Generic method with upper bounded wildcard
    public static double sumOfNumbers(List<? extends Number> list) {
        double sum = 0.0;
        for (Number n : list) {
            sum += n.doubleValue();
        }
        return sum;
    }
    
    // Generic method with lower bounded wildcard
    public static void addNumbers(List<? super Integer> list) {
        list.add(100);
        list.add(200);
    }
} 