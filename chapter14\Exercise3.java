package chapter14;

/**
 * Exercise 3: Create a generic class with two type parameters, and demonstrate
 * its use with different types of objects.
 */
public class Exercise3 {
    public static void main(String[] args) {
        // Test with String and Integer
        Pair<String, Integer> person = new Pair<>("<PERSON>", 25);
        System.out.println("Name: " + person.getFirst() + ", Age: " + person.getSecond());
        
        // Test with Double and Boolean
        Pair<Double, Boolean> result = new Pair<>(98.6, true);
        System.out.println("Temperature: " + result.getFirst() + ", Fever: " + result.getSecond());
        
        // Test with two strings
        Pair<String, String> credentials = new Pair<>("user123", "password456");
        System.out.println("Username: " + credentials.getFirst() + ", Password: " + credentials.getSecond());
        
        // Modify values
        credentials.setFirst("newUsername");
        credentials.setSecond("newPassword");
        System.out.println("Updated credentials - Username: " + credentials.getFirst() + 
                           ", Password: " + credentials.getSecond());
    }
}

class Pair<A, B> {
    private A first;
    private B second;
    
    public Pair(A first, B second) {
        this.first = first;
        this.second = second;
    }
    
    public A getFirst() { return first; }
    public B getSecond() { return second; }
    
    public void setFirst(A first) { this.first = first; }
    public void setSecond(B second) { this.second = second; }
} 