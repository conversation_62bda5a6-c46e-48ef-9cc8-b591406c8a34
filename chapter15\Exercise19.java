package chapter15;

import java.util.*;

/**
 * Exercise 19: Following the form of Store.java, build a model of a containerized
 * cargo ship.
 */

// Base class for products
class Product {
    private static long counter = 1;
    private final long id = counter++;
    private String description;
    private double price;
    
    public Product(String description, double price) {
        this.description = description;
        this.price = price;
    }
    
    @Override
    public String toString() {
        return id + ": " + description + ", price: $" + price;
    }
    
    // For display methods only
    public static Generator<Product> generator = 
        new Generator<Product>() {
            private Random rand = new Random();
            @Override
            public Product next() {
                return new Product("Product", 
                    Math.round(rand.nextDouble() * 1000.0) / 100.0);
            }
        };
}

// Different types of cargo
class Electronics extends Product {
    private String brand;
    
    public Electronics(String description, double price, String brand) {
        super(description, price);
        this.brand = brand;
    }
    
    @Override
    public String toString() {
        return super.toString() + ", Brand: " + brand;
    }
    
    // For display methods only
    public static Generator<Electronics> generator = 
        new Generator<Electronics>() {
            private Random rand = new Random();
            private String[] brands = {"Sony", "Samsung", "Apple", "LG", "Dell"};
            private String[] types = {"TV", "Laptop", "Phone", "Camera", "Tablet"};
            @Override
            public Electronics next() {
                String brand = brands[rand.nextInt(brands.length)];
                String type = types[rand.nextInt(types.length)];
                return new Electronics(brand + " " + type, 
                    Math.round(rand.nextDouble() * 2000.0) / 100.0,
                    brand);
            }
        };
}

class Clothing extends Product {
    private String size;
    
    public Clothing(String description, double price, String size) {
        super(description, price);
        this.size = size;
    }
    
    @Override
    public String toString() {
        return super.toString() + ", Size: " + size;
    }
    
    // For display methods only
    public static Generator<Clothing> generator = 
        new Generator<Clothing>() {
            private Random rand = new Random();
            private String[] types = {"Shirt", "Pants", "Dress", "Jacket", "Hat"};
            private String[] sizes = {"S", "M", "L", "XL", "XXL"};
            @Override
            public Clothing next() {
                String type = types[rand.nextInt(types.length)];
                String size = sizes[rand.nextInt(sizes.length)];
                return new Clothing(type, 
                    Math.round(rand.nextDouble() * 200.0) / 100.0,
                    size);
            }
        };
}

class Food extends Product {
    private String expirationDate;
    
    public Food(String description, double price, String expirationDate) {
        super(description, price);
        this.expirationDate = expirationDate;
    }
    
    @Override
    public String toString() {
        return super.toString() + ", Expires: " + expirationDate;
    }
    
    // For display methods only
    public static Generator<Food> generator = 
        new Generator<Food>() {
            private Random rand = new Random();
            private String[] types = {"Fruit", "Vegetables", "Meat", "Dairy", "Grain"};
            @Override
            public Food next() {
                String type = types[rand.nextInt(types.length)];
                // Generate a random expiration date (month/day/year)
                int month = rand.nextInt(12) + 1;
                int day = rand.nextInt(28) + 1;
                int year = 2024 + rand.nextInt(3);
                String expDate = month + "/" + day + "/" + year;
                return new Food(type, 
                    Math.round(rand.nextDouble() * 50.0) / 100.0,
                    expDate);
            }
        };
}

// Container holds a specific type of cargo
class Container<T extends Product> {
    private ArrayList<T> items = new ArrayList<>();
    private int capacity;
    
    public Container(int capacity) {
        this.capacity = capacity;
    }
    
    public boolean add(T item) {
        if(items.size() < capacity) {
            return items.add(item);
        }
        return false; // Container is full
    }
    
    public T get(int index) {
        return items.get(index);
    }
    
    public int size() {
        return items.size();
    }
    
    public int getCapacity() {
        return capacity;
    }
    
    @Override
    public String toString() {
        StringBuilder result = new StringBuilder("Container with " + 
            items.size() + "/" + capacity + " items:\n");
        for(T item : items) {
            result.append("  ").append(item).append("\n");
        }
        return result.toString();
    }
}

// Cargo ship holds different types of containers
class CargoShip {
    private ArrayList<Container<? extends Product>> containers = 
        new ArrayList<>();
    private int maxContainers;
    
    public CargoShip(int maxContainers) {
        this.maxContainers = maxContainers;
    }
    
    public boolean addContainer(Container<? extends Product> container) {
        if(containers.size() < maxContainers) {
            return containers.add(container);
        }
        return false; // Ship is full
    }
    
    public Container<? extends Product> getContainer(int index) {
        return containers.get(index);
    }
    
    public int getContainerCount() {
        return containers.size();
    }
    
    public int getMaxContainers() {
        return maxContainers;
    }
    
    @Override
    public String toString() {
        StringBuilder result = new StringBuilder("Cargo Ship with " + 
            containers.size() + "/" + maxContainers + " containers:\n");
        for(int i = 0; i < containers.size(); i++) {
            result.append("Container ").append(i + 1).append(": ")
                .append(containers.get(i).size()).append(" items\n");
        }
        return result.toString();
    }
    
    // Print a detailed report of all cargo
    public void printManifest() {
        System.out.println("=== CARGO SHIP MANIFEST ===");
        System.out.println("Total containers: " + containers.size() + 
            "/" + maxContainers);
        
        int totalItems = 0;
        for(Container<? extends Product> container : containers) {
            totalItems += container.size();
        }
        System.out.println("Total items: " + totalItems);
        
        for(int i = 0; i < containers.size(); i++) {
            System.out.println("\nCONTAINER " + (i + 1) + ":");
            Container<? extends Product> container = containers.get(i);
            System.out.println("Items: " + container.size() + 
                "/" + container.getCapacity());
            
            for(int j = 0; j < container.size(); j++) {
                System.out.println("  " + container.get(j));
            }
        }
        System.out.println("=== END OF MANIFEST ===");
    }
}

public class Exercise19 {
    public static void main(String[] args) {
        Random rand = new Random();
        
        // Create a cargo ship
        CargoShip ship = new CargoShip(5);
        
        // Create containers for different types of cargo
        Container<Electronics> electronicsContainer = new Container<>(10);
        Container<Clothing> clothingContainer = new Container<>(20);
        Container<Food> foodContainer = new Container<>(15);
        
        // Fill containers with random items
        for(int i = 0; i < 8; i++) {
            electronicsContainer.add(Electronics.generator.next());
        }
        
        for(int i = 0; i < 15; i++) {
            clothingContainer.add(Clothing.generator.next());
        }
        
        for(int i = 0; i < 12; i++) {
            foodContainer.add(Food.generator.next());
        }
        
        // Add containers to the ship
        ship.addContainer(electronicsContainer);
        ship.addContainer(clothingContainer);
        ship.addContainer(foodContainer);
        
        // Create a mixed container
        Container<Product> mixedContainer = new Container<>(10);
        for(int i = 0; i < 3; i++) {
            mixedContainer.add(Electronics.generator.next());
            mixedContainer.add(Clothing.generator.next());
            mixedContainer.add(Food.generator.next());
        }
        
        ship.addContainer(mixedContainer);
        
        // Print ship summary
        System.out.println(ship);
        
        // Print detailed manifest
        ship.printManifest();
    }
} 