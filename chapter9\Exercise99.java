// Exercise 9: Refactor Music5.java by moving the common methods in Wind, 
// Percussion and Stringed into an abstract class.

abstract class Instrument9 {
    // Common methods moved to abstract class
    public abstract void play();
    public abstract String what();
    public abstract void adjust();
}

class Wind9 extends Instrument9 {
    @Override
    public void play() {
        System.out.println("Wind9.play()");
    }
    
    @Override
    public String what() {
        return "Wind9";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Wind9");
    }
}

class Percussion9 extends Instrument9 {
    @Override
    public void play() {
        System.out.println("Percussion9.play()");
    }
    
    @Override
    public String what() {
        return "Percussion9";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Percussion9");
    }
}

class Stringed9 extends Instrument9 {
    @Override
    public void play() {
        System.out.println("Stringed9.play()");
    }
    
    @Override
    public String what() {
        return "Stringed9";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Stringed9");
    }
}

class Brass9 extends Wind9 {
    @Override
    public void play() {
        System.out.println("Brass9.play()");
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Brass9");
    }
}

class Woodwind9 extends Wind9 {
    @Override
    public void play() {
        System.out.println("Woodwind9.play()");
    }
    
    @Override
    public String what() {
        return "Woodwind9";
    }
}

public class Exercise99 {
    static void tune(Instrument9 i) {
        i.play();
    }
    
    static void tuneAll(Instrument9[] e) {
        for(Instrument9 i : e)
            tune(i);
    }
    
    public static void main(String[] args) {
        Instrument9[] orchestra = {
            new Wind9(),
            new Percussion9(),
            new Stringed9(),
            new Brass9(),
            new Woodwind9()
        };
        tuneAll(orchestra);
        
        System.out.println("\nExercise 9 completed successfully");
    }
} 