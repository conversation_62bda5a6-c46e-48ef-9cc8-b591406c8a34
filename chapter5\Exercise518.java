/**
 * 练习18：创建一个类，它包含有一个可变参数列表的构造器，
 * 用具有不同个数的参数分别调用这个构造器。
 */
class VarArgConstructor {
    private String[] items;
    
    // 使用可变参数列表的构造器
    public VarArgConstructor(String... args) {
        System.out.println("创建VarArgConstructor对象，参数个数: " + args.length);
        items = args;
    }
    
    // 打印所有项目
    public void printItems() {
        System.out.println("项目列表:");
        for(int i = 0; i < items.length; i++) {
            System.out.println("[" + i + "]: " + items[i]);
        }
        System.out.println();
    }
}

public class Exercise518 {
    public static void main(String[] args) {
        // 使用零个参数调用构造器
        System.out.println("调用VarArgConstructor():");
        VarArgConstructor vc1 = new VarArgConstructor();
        vc1.printItems();
        
        // 使用一个参数调用构造器
        System.out.println("调用VarArgConstructor(\"一个参数\"):");
        VarArgConstructor vc2 = new VarArgConstructor("一个参数");
        vc2.printItems();
        
        // 使用两个参数调用构造器
        System.out.println("调用VarArgConstructor(\"参数1\", \"参数2\"):");
        VarArgConstructor vc3 = new VarArgConstructor("参数1", "参数2");
        vc3.printItems();
        
        // 使用多个参数调用构造器
        System.out.println("调用VarArgConstructor(多个参数...):");
        VarArgConstructor vc4 = new VarArgConstructor(
            "Java", "Python", "C++", "JavaScript", "Go"
        );
        vc4.printItems();
        
        // 使用数组调用构造器
        System.out.println("使用数组调用VarArgConstructor:");
        String[] languages = {"Ruby", "Rust", "Swift"};
        VarArgConstructor vc5 = new VarArgConstructor(languages);
        vc5.printItems();
    }
} 