package chapter16;

import java.util.*;

/**
 * Exercise 3: Write a method that creates and initializes a two-dimensional
 * array of double. The size of the array is determined by the arguments of the
 * method, and the initialization values are a range determined by beginning
 * and ending values that are also arguments of the method. The method should
 * return the array.
 */

public class Exercise3 {
    /**
     * Creates and initializes a 2D array of doubles.
     * @param rows Number of rows in the array
     * @param cols Number of columns in the array
     * @param start Starting value for the range
     * @param end Ending value for the range
     * @return The initialized 2D array
     */
    public static double[][] create2DArray(int rows, int cols, double start, double end) {
        if (rows <= 0 || cols <= 0) {
            throw new IllegalArgumentException("Array dimensions must be positive");
        }
        
        double[][] result = new double[rows][cols];
        double step = (end - start) / (rows * cols - 1);
        
        for(int i = 0; i < rows; i++) {
            for(int j = 0; j < cols; j++) {
                // Calculate position in the flattened array
                int position = i * cols + j;
                // Set the value based on position in range
                result[i][j] = start + (step * position);
            }
        }
        
        return result;
    }
    
    // Helper method to print a 2D array with formatting
    public static void print2DArray(double[][] array) {
        for(int i = 0; i < array.length; i++) {
            for(int j = 0; j < array[i].length; j++) {
                System.out.printf("%9.4f ", array[i][j]);
            }
            System.out.println();
        }
    }
    
    public static void main(String[] args) {
        // Test case 1: Small array with integer range
        System.out.println("2x3 array with range 1.0 to 6.0:");
        double[][] array1 = create2DArray(2, 3, 1.0, 6.0);
        print2DArray(array1);
        
        // Test case 2: Larger array with decimal range
        System.out.println("\n4x4 array with range 0.0 to 15.0:");
        double[][] array2 = create2DArray(4, 4, 0.0, 15.0);
        print2DArray(array2);
        
        // Test case 3: Rectangular array with negative to positive range
        System.out.println("\n3x5 array with range -10.0 to 10.0:");
        double[][] array3 = create2DArray(3, 5, -10.0, 10.0);
        print2DArray(array3);
        
        // Test case 4: Small array with reversed range (end < start)
        System.out.println("\n2x2 array with reversed range (100.0 to 0.0):");
        double[][] array4 = create2DArray(2, 2, 100.0, 0.0);
        print2DArray(array4);
        
        // Verify our algorithm by checking first and last elements
        System.out.println("\nVerification:");
        System.out.println("First element should be start value: " + array1[0][0]);
        System.out.println("Last element should be end value: " + 
                           array1[array1.length-1][array1[0].length-1]);
        
        // Error case: Invalid dimensions
        try {
            System.out.println("\nTesting with invalid dimensions (0 rows):");
            double[][] errorArray = create2DArray(0, 5, 0.0, 10.0);
        } catch(IllegalArgumentException e) {
            System.out.println("Caught exception: " + e.getMessage());
        }
    }
} 