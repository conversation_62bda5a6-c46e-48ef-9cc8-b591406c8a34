package chapter14;

import java.util.ArrayList;
import java.util.List;

/**
 * Exercise 7: Demonstrate type erasure by creating a generic class and showing
 * how the runtime type information is lost during compilation.
 */
public class Exercise7 {
    public static void main(String[] args) {
        // Create instances of the generic class with different type arguments
        GenericType<String> stringType = new GenericType<>("Hello");
        GenericType<Integer> intType = new GenericType<>(42);
        
        // Print the class information
        System.out.println("stringType class: " + stringType.getClass().getName());
        System.out.println("intType class: " + intType.getClass().getName());
        
        // Compare class objects - they are the same due to type erasure
        System.out.println("stringType class == intType class: " 
                          + (stringType.getClass() == intType.getClass()));
        
        // Demonstrate erasure in collections
        List<String> stringList = new ArrayList<>();
        List<Integer> intList = new ArrayList<>();
        
        System.out.println("stringList class: " + stringList.getClass().getName());
        System.out.println("intList class: " + intList.getClass().getName());
        System.out.println("stringList class == intList class: " 
                          + (stringList.getClass() == intList.getClass()));
        
        // Runtime type checking
        stringType.showType();
        intType.showType();
        
        // Array of generic type - won't compile
        // GenericType<Integer>[] array = new GenericType<Integer>[10]; // Error
        
        // Workaround using raw type
        @SuppressWarnings("unchecked")
        GenericType<Integer>[] array = (GenericType<Integer>[]) new GenericType[10];
        array[0] = new GenericType<>(100);
        
        // This will compile but cause runtime issues
        // array[1] = new GenericType<String>("Problem"); // Runtime error if accessed as Integer
    }
}

class GenericType<T> {
    private T data;
    
    public GenericType(T data) {
        this.data = data;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public void showType() {
        // Can't get the actual type parameter at runtime
        System.out.println("Type parameter information is erased at runtime");
        System.out.println("Data value: " + data);
        System.out.println("Data class: " + data.getClass().getName());
    }
} 