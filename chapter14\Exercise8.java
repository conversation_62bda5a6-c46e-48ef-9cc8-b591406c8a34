package chapter14;

/**
 * Exercise 8: Create a hierarchy of classes and demonstrate the use of bounded type parameters
 * to restrict the types that can be used with a generic class.
 */
public class Exercise8 {
    public static void main(String[] args) {
        // Create instances with valid types
        BoundedBox<Integer> intBox = new BoundedBox<>(42);
        System.out.println("Integer value: " + intBox.getData());
        
        BoundedBox<Double> doubleBox = new BoundedBox<>(3.14);
        System.out.println("Double value: " + doubleBox.getData());
        
        BoundedBox<Float> floatBox = new BoundedBox<>(2.71f);
        System.out.println("Float value: " + floatBox.getData());
        
        // The following won't compile because String is not a Number:
        // BoundedBox<String> stringBox = new BoundedBox<>("Hello"); // Compile error
        
        // Using the bounded method
        System.out.println("\nComparing values:");
        System.out.println("42 > 10: " + compare(intBox, 10));
        System.out.println("3.14 > 10.0: " + compare(doubleBox, 10.0));
        System.out.println("2.71 > 3.0: " + compare(floatBox, 3.0f));
    }
    
    // Method with bounded type parameter
    public static <T extends Number> boolean compare(BoundedBox<T> box, T value) {
        return box.getData().doubleValue() > value.doubleValue();
    }
}

// Generic class with bounded type parameter
class BoundedBox<T extends Number> {
    private T data;
    
    public BoundedBox(T data) {
        this.data = data;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public double getDoubleValue() {
        return data.doubleValue();
    }
    
    public int getIntValue() {
        return data.intValue();
    }
} 