/**
 * 练习13：验证类中定义的static域在任何对象创建之前就已经进行初始化。
 */
class StaticTest13 {
    // 静态字段
    static String staticField = initStaticField();
    
    // 普通实例字段
    String instanceField = initInstanceField();
    
    // 静态方法，用于初始化静态字段
    static String initStaticField() {
        System.out.println("初始化静态字段 staticField");
        return "静态字段的值";
    }
    
    // 实例方法，用于初始化实例字段
    String initInstanceField() {
        System.out.println("初始化实例字段 instanceField");
        return "实例字段的值";
    }
    
    // 静态初始化块
    static {
        System.out.println("静态初始化块执行");
    }
    
    // 普通初始化块
    {
        System.out.println("普通初始化块执行");
    }
    
    // 构造器
    StaticTest13() {
        System.out.println("StaticTest构造器执行");
    }
}

public class Exercise513 {
    public static void main(String[] args) {
        System.out.println("main方法开始执行");
        
        // 在不创建对象的情况下访问静态字段
        System.out.println("在创建任何对象前，直接访问静态字段: " + StaticTest13.staticField);
        
        System.out.println("\n现在创建第一个对象:");
        StaticTest13 st1 = new StaticTest13();
        
        System.out.println("\n创建第二个对象:");
        StaticTest13 st2 = new StaticTest13();
        
        System.out.println("\n测试结论:");
        System.out.println("1. 静态字段和静态初始化块在任何对象创建之前就执行了初始化");
        System.out.println("2. 静态初始化只发生一次，即使创建多个对象");
        System.out.println("3. 实例字段和初始化块在每次创建对象时都会执行");
        System.out.println("4. 初始化顺序: 静态字段/块 -> 实例字段/块 -> 构造器");
    }
} 