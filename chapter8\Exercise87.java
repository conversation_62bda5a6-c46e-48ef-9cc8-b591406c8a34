// 练习7：创建一个用来放置Rodent（啮齿动物）的数组：Mouse（老鼠）、<PERSON><PERSON><PERSON>（鼹鼠）、<PERSON><PERSON>（仓鼠）等等，
// 这些类都是从Rodent派生的。在Rodent中声明方法，并在衍生类中覆盖这些方法，来展示它们的特性。通过Rodent数组调用这些方法。


class Rodent {
    public void eat() {
        System.out.println("Rodent eating");
    }
    
    public void sleep() {
        System.out.println("Rod<PERSON> sleeping");
    }
    
    public void run() {
        System.out.println("Rodent running");
    }
}

class Mouse extends Rodent {
    @Override
    public void eat() {
        System.out.println("Mouse nibbling on cheese");
    }
    
    @Override
    public void sleep() {
        System.out.println("Mouse sleeping in a tiny corner");
    }
    
    @Override
    public void run() {
        System.out.println("Mouse scurrying quickly");
    }
}

class G<PERSON><PERSON> extends Rodent {
    @Override
    public void eat() {
        System.out.println("Gerbil eating seeds");
    }
    
    @Override
    public void sleep() {
        System.out.println("<PERSON><PERSON><PERSON> sleeping in a burrow");
    }
    
    @Override
    public void run() {
        System.out.println("<PERSON><PERSON><PERSON> running on its wheel");
    }
}

class Hamster extends Rodent {
    @Override
    public void eat() {
        System.out.println("Hamster stuffing cheeks with food");
    }
    
    @Override
    public void sleep() {
        System.out.println("Hamster sleeping in its nest");
    }
    
    @Override
    public void run() {
        System.out.println("Hamster rolling in a ball");
    }
}

public class Exercise87 {
    public static void main(String[] args) {
        Rodent[] rodents = {
            new Mouse(),
            new Gerbil(),
            new Hamster()
        };
        
        for(Rodent r : rodents) {
            r.eat();
            r.sleep();
            r.run();
            System.out.println();
        }
    }
} 