/**
 * 练习11：修改前一个练习的程序，让你的finalize()方法打印出一条消息，
 * 但不要调用System.gc()方法，而是依靠垃圾回收器的运行来验证finalize()方法是否有效。
 */
class WithFinalize2 {
    private static int counter = 0;
    private int id = counter++;
    
    // 构造器
    public WithFinalize2() {
        System.out.println("WithFinalize2 " + id + " 被创建");
    }
    
    // finalize方法
    protected void finalize() {
        System.out.println("WithFinalize2 " + id + " 的finalize()方法被调用");
        System.out.println("对象 " + id + " 正在被垃圾回收器自然回收...");
    }
}

public class Exercise511 {
    public static void main(String[] args) {
        System.out.println("创建多个对象并等待自然垃圾回收");
        
        // 循环创建大量对象，不保留引用，让垃圾回收器自然运行
        for(int i = 0; i < 100000; i++) {
            new WithFinalize2();
        }
        
        System.out.println("创建了100000个对象，不保留任何引用");
        System.out.println("等待自然垃圾回收...");
        
        // 创建更多对象，促使JVM触发垃圾回收
        System.out.println("创建更多大对象以促使JVM进行垃圾回收");
        for(int i = 0; i < 10; i++) {
            try {
                // 创建大型数组并等待
                byte[] bigArray = new byte[10000000]; // 10MB
                Thread.sleep(100);
            } catch(Exception e) {
                e.printStackTrace();
            }
        }
        
        System.out.println("\n程序行为解释:");
        System.out.println("1. 创建了大量WithFinalize2类的对象但不保留引用");
        System.out.println("2. 内存压力增加，JVM可能会自动触发垃圾回收");
        System.out.println("3. 如果finalize()方法被调用，则证明垃圾回收器在运行");
        System.out.println("4. 但由于垃圾回收器的运行时间是不确定的，可能看不到任何输出");
        System.out.println("5. 此程序依赖于JVM的内存管理策略，在不同环境中行为可能不同");
    }
} 