package chapter13;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

/**
 * Exercise 13.5: Thread Deadlock, Livelock, and Starvation
 * 
 * This exercise demonstrates common concurrency problems:
 * 1. Deadlock - where two or more threads are blocked forever, waiting for each other
 * 2. Livelock - where threads keep changing their states in response to others without making progress
 * 3. Starvation - where a thread is unable to gain regular access to shared resources
 */
public class Exercise135 {
    
    public static void main(String[] args) {
        System.out.println("==== Thread Deadlock, Livelock, and Starvation ====");
        
        // Uncomment only one example at a time for better observation
        
        // 1. Deadlock example
        deadlockExample();
        
        // 2. Livelock example
        // livelockExample();
        
        // 3. Starvation example
        // starvationExample();
    }
    
    /**
     * Demonstrates a deadlock scenario where two threads each hold a resource
     * that the other needs, and neither will release their resource until they
     * acquire the other resource, resulting in both threads waiting forever.
     */
    private static void deadlockExample() {
        System.out.println("\n1. Deadlock Example:");
        System.out.println("Note: This example will create an actual deadlock.");
        System.out.println("You will need to terminate the program manually after observing the deadlock.");
        
        final Object resource1 = new Object();
        final Object resource2 = new Object();
        
        // Thread 1: Tries to lock resource1 then resource2
        Thread thread1 = new Thread(() -> {
            System.out.println("Thread 1: Attempting to lock resource 1");
            synchronized (resource1) {
                System.out.println("Thread 1: Locked resource 1");
                
                try {
                    Thread.sleep(100); // Delay to ensure thread 2 gets resource 2
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                
                System.out.println("Thread 1: Attempting to lock resource 2");
                synchronized (resource2) {
                    System.out.println("Thread 1: Locked resource 2");
                    
                    // Work with both resources
                    System.out.println("Thread 1: Working with both resources");
                }
                System.out.println("Thread 1: Released resource 2");
            }
            System.out.println("Thread 1: Released resource 1");
        }, "DeadlockThread-1");
        
        // Thread 2: Tries to lock resource2 then resource1
        Thread thread2 = new Thread(() -> {
            System.out.println("Thread 2: Attempting to lock resource 2");
            synchronized (resource2) {
                System.out.println("Thread 2: Locked resource 2");
                
                try {
                    Thread.sleep(100); // Delay to ensure thread 1 gets resource 1
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                
                System.out.println("Thread 2: Attempting to lock resource 1");
                synchronized (resource1) {
                    System.out.println("Thread 2: Locked resource 1");
                    
                    // Work with both resources
                    System.out.println("Thread 2: Working with both resources");
                }
                System.out.println("Thread 2: Released resource 1");
            }
            System.out.println("Thread 2: Released resource 2");
        }, "DeadlockThread-2");
        
        thread1.start();
        thread2.start();
        
        // Solution to prevent deadlock (not used in this example):
        // 1. Always acquire locks in the same order
        // 2. Use tryLock with timeout instead of blocking lock acquisition
        // 3. Use higher-level concurrency utilities like ReentrantLock
        
        System.out.println("Deadlock example is running. Program will remain hung in deadlock.");
        System.out.println("Press Ctrl+C to terminate after observing the deadlock behavior.");
    }
    
    /**
     * Demonstrates a livelock scenario where two threads keep responding to
     * each other's actions, but neither makes progress.
     */
    private static void livelockExample() {
        System.out.println("\n2. Livelock Example:");
        
        // Create shared resources
        final Worker worker1 = new Worker("Worker 1");
        final Worker worker2 = new Worker("Worker 2");
        
        // Create a shared resource
        final SharedResource sharedResource = new SharedResource(worker1);
        
        // Thread for worker1
        Thread thread1 = new Thread(() -> {
            worker1.work(sharedResource, worker2);
        });
        
        // Thread for worker2
        Thread thread2 = new Thread(() -> {
            worker2.work(sharedResource, worker1);
        });
        
        thread1.start();
        thread2.start();
        
        try {
            // Let the livelock run for 5 seconds
            Thread.sleep(5000);
            
            // Interrupt both threads to stop the livelock
            thread1.interrupt();
            thread2.interrupt();
            
            // Wait for both threads to finish
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        System.out.println("Livelock example completed. Note how both threads kept changing states without making progress.");
    }
    
    /**
     * Demonstrates a starvation scenario where low-priority threads
     * are unable to get CPU time because high-priority threads dominate.
     */
    private static void starvationExample() {
        System.out.println("\n3. Starvation Example:");
        
        final Object sharedResource = new Object();
        
        // Create a greedy thread with high priority
        Thread greedyThread = new Thread(() -> {
            System.out.println("Greedy thread starting");
            
            // Hold the lock for a long time
            synchronized (sharedResource) {
                System.out.println("Greedy thread acquired the lock");
                try {
                    for (int i = 0; i < 5; i++) {
                        System.out.println("Greedy thread working... " + i);
                        Thread.sleep(1000);
                    }
                } catch (InterruptedException e) {
                    System.out.println("Greedy thread interrupted");
                }
                System.out.println("Greedy thread releasing the lock");
            }
            
            // Keep running computations, taking up CPU time
            try {
                for (int i = 0; i < 10; i++) {
                    System.out.println("Greedy thread consuming CPU... " + i);
                    Thread.sleep(500);
                }
            } catch (InterruptedException e) {
                System.out.println("Greedy thread interrupted");
            }
            
            System.out.println("Greedy thread finished");
        });
        
        // Set high priority
        greedyThread.setPriority(Thread.MAX_PRIORITY);
        
        // Create several starving threads with low priority
        Thread[] starvingThreads = new Thread[3];
        for (int i = 0; i < starvingThreads.length; i++) {
            final int threadId = i;
            starvingThreads[i] = new Thread(() -> {
                System.out.println("Starving thread " + threadId + " starting");
                
                try {
                    // Try to acquire the lock
                    System.out.println("Starving thread " + threadId + " waiting for lock");
                    synchronized (sharedResource) {
                        System.out.println("Starving thread " + threadId + " finally acquired the lock!");
                        Thread.sleep(100);
                    }
                    System.out.println("Starving thread " + threadId + " released the lock");
                    
                    // Try to get some CPU time
                    for (int j = 0; j < 3; j++) {
                        System.out.println("Starving thread " + threadId + " trying to compute... " + j);
                        Thread.sleep(200);
                    }
                } catch (InterruptedException e) {
                    System.out.println("Starving thread " + threadId + " interrupted");
                }
                
                System.out.println("Starving thread " + threadId + " finished");
            });
            
            // Set low priority
            starvingThreads[i].setPriority(Thread.MIN_PRIORITY);
        }
        
        // Start all threads
        greedyThread.start();
        for (Thread thread : starvingThreads) {
            thread.start();
        }
        
        // Wait for all threads to finish
        try {
            greedyThread.join();
            for (Thread thread : starvingThreads) {
                thread.join();
            }
        } catch (InterruptedException e) {
            System.out.println("Main thread interrupted");
        }
        
        System.out.println("Starvation example completed. Notice how the low-priority threads had to wait longer for resources.");
    }
    
    // Helper classes for livelock example
    
    static class Worker {
        private final String name;
        private boolean active;
        
        public Worker(String name) {
            this.name = name;
            this.active = true;
        }
        
        public String getName() {
            return name;
        }
        
        public boolean isActive() {
            return active;
        }
        
        public void work(SharedResource sharedResource, Worker otherWorker) {
            while (active) {
                // Check if resource owner is the other worker
                if (sharedResource.getOwner() == otherWorker) {
                    System.out.println(name + ": Waiting for resource owned by " + otherWorker.getName());
                    try {
                        Thread.sleep(100); // Small delay
                    } catch (InterruptedException e) {
                        System.out.println(name + " was interrupted");
                        active = false; // Exit loop on interruption
                        break;
                    }
                    continue;
                }
                
                // Check if other worker is active
                if (!otherWorker.isActive()) {
                    System.out.println(name + ": Other worker is no longer active, taking the resource");
                    active = false; // Exit loop
                    break;
                }
                
                // Try to acquire the resource
                if (sharedResource.getOwner() == this) {
                    System.out.println(name + ": Working on the resource");
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        System.out.println(name + " was interrupted");
                        active = false; // Exit loop on interruption
                        break;
                    }
                    
                    // Politely give up the resource
                    System.out.println(name + ": Politely giving up the resource to " + otherWorker.getName());
                    sharedResource.setOwner(otherWorker);
                } else {
                    // Try to get the resource
                    System.out.println(name + ": Trying to acquire the resource");
                    sharedResource.setOwner(this);
                }
            }
            
            System.out.println(name + " finished working");
        }
    }
    
    static class SharedResource {
        private Worker owner;
        
        public SharedResource(Worker owner) {
            this.owner = owner;
        }
        
        public synchronized Worker getOwner() {
            return owner;
        }
        
        public synchronized void setOwner(Worker owner) {
            this.owner = owner;
        }
    }
} 