package chapter12;

// 练习2：使用字节流（FileInputStream和FileOutputStream）进行文件读写

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class Exercise122 {
    public static void main(String[] args) {
        // 定义文件路径
        File sourceFile = new File("chapter12/source.txt");
        File targetFile = new File("chapter12/target.txt");
        
        try {
            // 确保目录存在
            sourceFile.getParentFile().mkdirs();
            
            // 1. 使用FileOutputStream写入数据
            System.out.println("1. 使用FileOutputStream写入数据：");
            
            // 创建输出流
            try (FileOutputStream fos = new FileOutputStream(sourceFile)) {
                // 写入字符串数据
                String data = "Hello, FileOutputStream!\n这是第二行文本。\n这是第三行文本，包含中文字符。";
                byte[] bytes = data.getBytes();
                
                // 写入全部字节
                fos.write(bytes);
                System.out.println("成功写入 " + bytes.length + " 字节到文件: " + sourceFile.getPath());
                
                // 确保数据被写入到磁盘
                fos.flush();
            }
            
            // 2. 使用FileInputStream读取数据
            System.out.println("\n2. 使用FileInputStream读取整个文件：");
            
            // 创建输入流
            try (FileInputStream fis = new FileInputStream(sourceFile)) {
                // 获取文件大小
                int fileSize = (int) sourceFile.length();
                byte[] buffer = new byte[fileSize];
                
                // 读取全部内容到缓冲区
                int bytesRead = fis.read(buffer);
                
                // 转换为字符串并打印
                String fileContent = new String(buffer, 0, bytesRead);
                System.out.println("读取到的文件内容：");
                System.out.println("--------------------");
                System.out.println(fileContent);
                System.out.println("--------------------");
                System.out.println("成功读取 " + bytesRead + " 字节");
            }
            
            // 3. 逐字节读取文件内容
            System.out.println("\n3. 使用FileInputStream逐字节读取：");
            
            try (FileInputStream fis = new FileInputStream(sourceFile)) {
                int byteData;
                int count = 0;
                System.out.print("前10个字节的十六进制值：");
                
                // 逐字节读取，读到文件末尾返回-1
                while ((byteData = fis.read()) != -1) {
                    if (count < 10) {
                        System.out.print(String.format("%02X ", byteData));
                    }
                    count++;
                    
                    // 只显示前10个字节，避免输出过多
                    if (count == 10) {
                        System.out.println("...");
                    }
                }
                
                System.out.println("\n成功读取 " + count + " 字节");
            }
            
            // 4. 使用缓冲区批量读取
            System.out.println("\n4. 使用缓冲区批量读取：");
            
            try (FileInputStream fis = new FileInputStream(sourceFile)) {
                byte[] buffer = new byte[1024]; // 1KB缓冲区
                int bytesRead;
                int totalBytes = 0;
                
                // 批量读取数据到缓冲区
                while ((bytesRead = fis.read(buffer)) != -1) {
                    totalBytes += bytesRead;
                    
                    System.out.println("读取了 " + bytesRead + " 字节到缓冲区");
                    System.out.println("缓冲区内容预览: " + new String(buffer, 0, Math.min(bytesRead, 20)) + "...");
                }
                
                System.out.println("总共读取 " + totalBytes + " 字节");
            }
            
            // 5. 复制文件
            System.out.println("\n5. 复制文件：");
            
            try (FileInputStream fis = new FileInputStream(sourceFile);
                 FileOutputStream fos = new FileOutputStream(targetFile)) {
                
                byte[] buffer = new byte[4096]; // 4KB缓冲区
                int bytesRead;
                int totalBytes = 0;
                
                // 从输入流读取数据并写入到输出流
                while ((bytesRead = fis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                System.out.println("文件复制成功!");
                System.out.println("从 " + sourceFile.getPath() + " 复制到 " + targetFile.getPath());
                System.out.println("复制了 " + totalBytes + " 字节");
            }
            
            // 6. 追加写入文件
            System.out.println("\n6. 追加内容到文件：");
            
            try (FileOutputStream fos = new FileOutputStream(targetFile, true)) {  // true表示追加模式
                String appendData = "\n\n这是追加的内容。\n这是第二行追加的内容。";
                byte[] bytes = appendData.getBytes();
                
                fos.write(bytes);
                System.out.println("成功追加 " + bytes.length + " 字节到文件: " + targetFile.getPath());
            }
            
            // 7. 读取追加后的文件
            System.out.println("\n7. 读取追加后的文件：");
            
            try (FileInputStream fis = new FileInputStream(targetFile)) {
                byte[] buffer = new byte[(int) targetFile.length()];
                int bytesRead = fis.read(buffer);
                
                String fileContent = new String(buffer, 0, bytesRead);
                System.out.println("读取到的文件内容：");
                System.out.println("--------------------");
                System.out.println(fileContent);
                System.out.println("--------------------");
            }
            
            // 8. 跳过部分字节读取
            System.out.println("\n8. 跳过部分字节读取：");
            
            try (FileInputStream fis = new FileInputStream(targetFile)) {
                // 跳过前20个字节
                long skipped = fis.skip(20);
                System.out.println("跳过了 " + skipped + " 字节");
                
                // 读取接下来的50个字节
                byte[] buffer = new byte[50];
                int bytesRead = fis.read(buffer);
                
                String partialContent = new String(buffer, 0, bytesRead);
                System.out.println("跳过20字节后读取的内容：");
                System.out.println("--------------------");
                System.out.println(partialContent);
                System.out.println("--------------------");
            }
            
            // 9. 文件比较
            System.out.println("\n9. 文件内容比较：");
            compareFiles(sourceFile, targetFile);
            
            // 10. 清理文件
            System.out.println("\n10. 清理测试文件：");
            if (sourceFile.delete()) {
                System.out.println("已删除源文件: " + sourceFile.getPath());
            }
            
            if (targetFile.delete()) {
                System.out.println("已删除目标文件: " + targetFile.getPath());
            }
            
        } catch (IOException e) {
            System.out.println("发生I/O异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 比较两个文件的内容
    private static void compareFiles(File file1, File file2) throws IOException {
        if (!file1.exists() || !file2.exists()) {
            System.out.println("文件不存在，无法比较");
            return;
        }
        
        if (file1.length() == file2.length()) {
            boolean contentEquals = true;
            
            try (FileInputStream fis1 = new FileInputStream(file1);
                 FileInputStream fis2 = new FileInputStream(file2)) {
                
                int byte1, byte2;
                int position = 0;
                
                // 逐字节比较两个文件
                while ((byte1 = fis1.read()) != -1) {
                    byte2 = fis2.read();
                    
                    if (byte1 != byte2) {
                        contentEquals = false;
                        System.out.println("文件内容在位置 " + position + " 处不同:");
                        System.out.println(file1.getName() + ": " + String.format("%02X", byte1));
                        System.out.println(file2.getName() + ": " + String.format("%02X", byte2));
                        break;
                    }
                    
                    position++;
                }
                
                if (contentEquals) {
                    System.out.println("两个文件内容完全相同");
                } else {
                    System.out.println("两个文件内容不同");
                }
            }
        } else {
            System.out.println("两个文件大小不同:");
            System.out.println(file1.getName() + ": " + file1.length() + " 字节");
            System.out.println(file2.getName() + ": " + file2.length() + " 字节");
        }
    }
} 