package chapter9.serviceimpl;

import chapter9.interfaces.Service;

public class Implementation1 implements Service {
    @Override
    public void method1() {
        System.out.println("Implementation1 method1");
    }

    @Override
    public void method2() {
        System.out.println("Implementation1 method2");
    }
    
    protected Service createService() {
        return new Implementation1();
    }
    
    public Service getService() {
        return new Implementation1();
    }
} 