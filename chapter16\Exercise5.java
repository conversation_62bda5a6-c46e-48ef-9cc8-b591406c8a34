package chapter16;

import java.util.*;

/**
 * Exercise 5: Demonstrate that multidimensional arrays of objects are
 * automatically initialized to null.
 */
public class Exercise5 {
    public static void main(String[] args) {
        // Create a 2D array of Object
        System.out.println("2D array of Object:");
        Object[][] a = new Object[3][3];
        
        // Print the array to show that all elements are initialized to null
        for(int i = 0; i < a.length; i++) {
            for(int j = 0; j < a[i].length; j++) {
                System.out.print(a[i][j] + " ");
            }
            System.out.println();
        }
        
        // Create a 3D array of String
        System.out.println("\n3D array of String:");
        String[][][] b = new String[2][2][2];
        
        // Print the array to show that all elements are initialized to null
        for(int i = 0; i < b.length; i++) {
            for(int j = 0; j < b[i].length; j++) {
                for(int k = 0; k < b[i][j].length; k++) {
                    System.out.print(b[i][j][k] + " ");
                }
                System.out.println();
            }
            System.out.println();
        }
        
        // Create an array of a custom class
        System.out.println("2D array of BerylliumSphere:");
        BerylliumSphere[][] c = new BerylliumSphere[2][2];
        
        // Print the array to show that all elements are initialized to null
        for(int i = 0; i < c.length; i++) {
            for(int j = 0; j < c[i].length; j++) {
                System.out.print(c[i][j] + " ");
            }
            System.out.println();
        }
        
        // Verify that the elements are actually null
        System.out.println("\nVerification using == null:");
        for(int i = 0; i < c.length; i++) {
            for(int j = 0; j < c[i].length; j++) {
                System.out.print((c[i][j] == null) + " ");
            }
            System.out.println();
        }
    }
}

class BerylliumSphere {
    private static long counter;
    private final long id = counter++;
    public String toString() { return "Sphere " + id; }
} 