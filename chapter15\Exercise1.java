package chapter15;

/**
 * Exercise 1: Use Holders with the typeinfo.pets library to show that
 * a Holders that holds a base type can also hold a derived type.
 */

// Simple generic holder class
class Holder<T> {
    private T value;
    
    public Holder() {}
    
    public Holder(T val) { value = val; }
    
    public void set(T val) { value = val; }
    
    public T get() { return value; }
    
    @Override
    public boolean equals(Object obj) {
        return value.equals(obj);
    }
}

// Pet hierarchy for demonstration
class Pet {
    private String name;
    public Pet(String name) { this.name = name; }
    public String getName() { return name; }
    @Override
    public String toString() { return name; }
}

class Dog extends Pet {
    public Dog(String name) { super(name); }
}

class Cat extends Pet {
    public Cat(String name) { super(name); }
}

public class Exercise1 {
    public static void main(String[] args) {
        // Create a Holder for the base type Pet
        Holder<Pet> petHolder = new Holder<Pet>();
        
        // Add a Pet object
        Pet pet = new Pet("Generic Pet");
        petHolder.set(pet);
        System.out.println("petHolder contains: " + petHolder.get());
        
        // Now add a Dog object (derived type)
        Dog dog = new Dog("Spot");
        petHolder.set(dog);
        System.out.println("petHolder now contains: " + petHolder.get());
        
        // And a Cat object (another derived type)
        Cat cat = new Cat("Whiskers");
        petHolder.set(cat);
        System.out.println("petHolder now contains: " + petHolder.get());
        
        // Demonstrate that we can still get the specific type
        Pet retrievedPet = petHolder.get();
        System.out.println("Retrieved pet: " + retrievedPet);
        
        // We need to cast if we want to access specific derived class methods
        if(retrievedPet instanceof Cat) {
            Cat retrievedCat = (Cat)retrievedPet;
            System.out.println("Retrieved a Cat named: " + retrievedCat.getName());
        }
        
        System.out.println("\nThis demonstrates that a Holder of a base type (Pet) " +
            "can also hold derived types (Dog and Cat).");
    }
} 