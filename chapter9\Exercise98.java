// Exercise 8: In polymorphism.Sandwich.java, create an interface
// called FastFood (with appropriate methods) and change Sandwich
// so that it also implements FastFood.

interface FastFood {
    void prepareQuickly();
    void serveInBag();
    float calorieCount();
}

class Meal {
    Meal() { System.out.println("Meal()"); }
}

class Bread {
    Bread() { System.out.println("Bread()"); }
}

class Cheese {
    Cheese() { System.out.println("Cheese()"); }
}

class Lettuce {
    Lettuce() { System.out.println("Lettuce()"); }
}

class Lunch extends Meal {
    Lunch() { System.out.println("Lunch()"); }
}

class PortableLunch extends Lunch {
    PortableLunch() { System.out.println("PortableLunch()"); }
}

class Sandwich extends PortableLunch implements FastFood {
    private Bread b = new Bread();
    private Cheese c = new Cheese();
    private Lettuce l = new Lettuce();
    
    public Sandwich() { System.out.println("Sandwich()"); }
    
    @Override
    public void prepareQuickly() {
        System.out.println("Making sandwich in under 3 minutes");
    }
    
    @Override
    public void serveInBag() {
        System.out.println("Placing sandwich in paper bag");
    }
    
    @Override
    public float calorieCount() {
        return 450.0f;
    }
}

public class Exercise98 {
    public static void main(String[] args) {
        Sandwich sandwich = new Sandwich();
        sandwich.prepareQuickly();
        sandwich.serveInBag();
        System.out.println("Calories: " + sandwich.calorieCount());
        
        // You can also use it as a FastFood
        FastFood lunch = sandwich;
        lunch.prepareQuickly();
        lunch.serveInBag();
        System.out.println("Calories: " + lunch.calorieCount());
        
        System.out.println("\nExercise 8 completed successfully");
    }
} 