package chapter16;

import java.util.*;

/**
 * Exercise 1: Create a method that takes an array of BerylliumSphere as an
 * argument. Call the method, creating the argument dynamically. Demonstrate
 * that ordinary aggregate array initialization doesn't work in this case.
 * Discover the only situations where ordinary aggregate array initialization
 * works, and where dynamic aggregate initialization is redundant.
 */

// BerylliumSphere class from the book
class BerylliumSphere {
    private static long counter;
    private final long id = counter++;
    
    @Override
    public String toString() {
        return "Sphere " + id;
    }
}

public class Exercise1 {
    // Method that takes an array of BerylliumSphere
    public static void showArray(BerylliumSphere[] spheres) {
        System.out.println("Array reference: " + spheres);
        System.out.println("Array contents: " + Arrays.toString(spheres));
    }
    
    // Method that takes a variable argument list of BerylliumSphere
    public static void showVarargs(BerylliumSphere... spheres) {
        System.out.println("Varargs reference: " + spheres);
        System.out.println("Varargs contents: " + Arrays.toString(spheres));
    }
    
    public static void main(String[] args) {
        // Attempt at ordinary aggregate array initialization
        // This won't compile:
        // BerylliumSphere[] a = { new BerylliumSphere(), new BerylliumSphere() };
        
        // Dynamic aggregate array initialization works:
        BerylliumSphere[] b = new BerylliumSphere[] { 
            new BerylliumSphere(), new BerylliumSphere() 
        };
        showArray(b);
        
        // Creating the argument dynamically inline works:
        showArray(new BerylliumSphere[] { 
            new BerylliumSphere(), new BerylliumSphere()
        });
        
        // Testing varargs as an alternative
        showVarargs(new BerylliumSphere(), new BerylliumSphere());
        
        // Empty array demonstration
        showArray(new BerylliumSphere[0]);
        showVarargs();
        
        System.out.println("\nDemonstrating when ordinary aggregate initialization works:");
        
        // Works for primitives
        int[] intArray = { 1, 2, 3, 4, 5 };
        System.out.println("int array: " + Arrays.toString(intArray));
        
        // Works for String
        String[] stringArray = { "one", "two", "three" };
        System.out.println("String array: " + Arrays.toString(stringArray));
        
        // Works when assigning to an array reference
        BerylliumSphere[] sphereArray;
        sphereArray = new BerylliumSphere[] { 
            new BerylliumSphere(), new BerylliumSphere() 
        };
        System.out.println("BerylliumSphere array: " + Arrays.toString(sphereArray));
        
        System.out.println("\nConclusion:");
        System.out.println("1. Ordinary aggregate initialization works for primitive arrays " +
            "and arrays of common built-in types like String");
        System.out.println("2. For arrays of user-defined classes as method arguments, " +
            "you must use dynamic aggregate initialization");
        System.out.println("3. Dynamic aggregate initialization is redundant when using varargs, " +
            "which provides a more convenient syntax");
    }
} 