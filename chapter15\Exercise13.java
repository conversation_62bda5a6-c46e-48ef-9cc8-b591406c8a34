package chapter15;

import java.util.*;

/**
 * Exercise 13: Overload the fill() method so that the arguments and return types
 * are the specific subtypes of Collection: List, Queue and Set. This way, you
 * don't lose the type of container. Can you overload to distinguish between
 * List and LinkedList?
 */

// Generator interface
interface Generator13<T> {
    T next();
}

// String generator for testing
class StringGenerator implements Generator13<String> {
    private int count = 0;
    private String[] words = "The quick brown fox jumps over the lazy dog".split(" ");
    
    @Override
    public String next() {
        return words[count++ % words.length];
    }
}

// Integer generator for testing
class IntegerGenerator implements Generator13<Integer> {
    private int count = 0;
    
    @Override
    public Integer next() {
        return count++;
    }
}

// Overloaded fill methods for different collection types
class Generators {
    // Generic method to fill any Collection
    public static <T> Collection<T> fill(Collection<T> coll, Generator13<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            coll.add(gen.next());
        }
        return coll;
    }
    
    // Overloaded method for List
    public static <T> List<T> fill(List<T> list, Generator13<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            list.add(gen.next());
        }
        return list;
    }
    
    // Overloaded method for LinkedList
    public static <T> LinkedList<T> fill(LinkedList<T> lList, Generator13<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            lList.add(gen.next());
        }
        return lList;
    }
    
    // Overloaded method for Set
    public static <T> Set<T> fill(Set<T> set, Generator13<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            set.add(gen.next());
        }
        return set;
    }
    
    // Overloaded method for Queue
    public static <T> Queue<T> fill(Queue<T> queue, Generator13<T> gen, int n) {
        for(int i = 0; i < n; i++) {
            queue.offer(gen.next());
        }
        return queue;
    }
}

public class Exercise13 {
    public static void main(String[] args) {
        // Create generators
        StringGenerator strGen = new StringGenerator();
        IntegerGenerator intGen = new IntegerGenerator();
        
        // Test with general Collection (ArrayList)
        Collection<String> coll = Generators.fill(
            new ArrayList<String>(), strGen, 5);
        System.out.println("Collection: " + coll);
        
        // Test with ArrayList (uses List overload)
        List<String> list = Generators.fill(
            new ArrayList<String>(), strGen, 5);
        System.out.println("List: " + list);
        
        // Test with LinkedList (uses LinkedList overload)
        LinkedList<String> lList = Generators.fill(
            new LinkedList<String>(), strGen, 5);
        System.out.println("LinkedList: " + lList);
        
        // Test with HashSet (uses Set overload)
        Set<String> set = Generators.fill(
            new HashSet<String>(), strGen, 10); // More items to show Set behavior
        System.out.println("Set: " + set);
        
        // Test with Queue (uses Queue overload)
        Queue<Integer> queue = Generators.fill(
            new LinkedList<Integer>(), intGen, 5);
        System.out.println("Queue: " + queue);
        
        // Demonstrate that the specific type is preserved
        lList.addFirst("First"); // LinkedList-specific method
        System.out.println("After addFirst(): " + lList);
        
        // Test overload resolution
        ArrayList<String> arrayList = new ArrayList<>();
        Generators.fill(arrayList, strGen, 3);  // Calls List<T> fill(...)
        
        LinkedList<String> linkedList = new LinkedList<>();
        Generators.fill(linkedList, strGen, 3); // Calls LinkedList<T> fill(...)
        
        System.out.println("\nArrayList filled via List overload: " + arrayList);
        System.out.println("LinkedList filled via LinkedList overload: " + linkedList);
        
        // What if we pass a LinkedList to a method expecting List?
        List<String> listRef = linkedList;
        Generators.fill(listRef, strGen, 3);    // Calls List<T> fill(...)
        System.out.println("LinkedList (via List reference) filled via List overload: " + listRef);
    }
} 