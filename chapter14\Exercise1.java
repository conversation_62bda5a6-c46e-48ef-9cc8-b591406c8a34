package chapter14;

/**
 * Exercise 1: Create a Holder class that holds one object of any type.
 * Demonstrate its use with different types of objects.
 */
public class Exercise1 {
    public static void main(String[] args) {
        // Test with different types
        Holder<Integer> intHolder = new Holder<>(42);
        System.out.println("Integer value: " + intHolder.get());
        
        Holder<String> stringHolder = new Holder<>("Hello Generics");
        System.out.println("String value: " + stringHolder.get());
        
        Holder<Double> doubleHolder = new Holder<>(3.14159);
        System.out.println("Double value: " + doubleHolder.get());
    }
}

class Holder<T> {
    private T value;
    
    public Holder(T value) {
        this.value = value;
    }
    
    public T get() {
        return value;
    }
    
    public void set(T value) {
        this.value = value;
    }
} 