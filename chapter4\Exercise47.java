import static net.mindview.util.Print.print;

public class Exercise47 {
    static void Count1(int n) {
        for(int i = 0; i < n; i++) {
            print(i + 1);
            if(i == 99)
                break;
        }
    }
    static void Count2(int n) {
        for(int i = 0; i < n; i++) {
            print(i + 1);
            if(i == 99)
                return;
        }
    }
    public static void main(String[] args) {
        Count1(100);
        Count2(100);
    }
}
