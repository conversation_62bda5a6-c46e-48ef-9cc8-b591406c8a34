public class Exercise410 {
//    吸血鬼数字是指位数为偶数的自然数，它可以由一对数字相乘得到，
//    这对数字各包含乘积一半位数的数字，并且这对数字中数字的组合与乘积数字的组合完全相同。
    //通过将四位数 i 除以 1000，得到该四位数的千位数字。
    static int a(int i) {
        return i / 1000;
    }
//  先对 i 取模 1000，得到后三位数，再将结果除以 100，得到该四位数的百位数字。
    static int b(int i) {
        return (i % 1000) / 100;
    }
//先对 i 取模 1000 得到后三位数，再对结果取模 100 得到后两位数，最后将其除以 10，得到该四位数的十位数字。
    static int c(int i) {
        return ((i % 1000) % 100) / 10;
    }
//  先对 i 取模 1000 得到后三位数，再对结果取模 100 得到后两位数，最后对其取模 10，得到该四位数的个位数字。
    static int d(int i) {
        return ((i % 1000) % 100) % 10;
    }

    static int com(int i, int j) {
        return (i * 10) + j;
    }

    static void productTest(int i, int m, int n) {
        if (m * n == i) System.out.println(i + " = " + m + " * " + n);
    }

    public static void main(String[] args) {
        for (int i = 1001; i < 9999; i++) {
            productTest(i, com(a(i), b(i)), com(c(i), d(i)));
            productTest(i, com(a(i), b(i)), com(d(i), c(i)));
            productTest(i, com(a(i), c(i)), com(b(i), d(i)));
            productTest(i, com(a(i), c(i)), com(d(i), b(i)));
            productTest(i, com(a(i), d(i)), com(b(i), c(i)));
            productTest(i, com(a(i), d(i)), com(c(i), b(i)));
            productTest(i, com(b(i), a(i)), com(c(i), d(i)));
            productTest(i, com(b(i), a(i)), com(d(i), c(i)));
            productTest(i, com(b(i), c(i)), com(d(i), a(i)));
            productTest(i, com(b(i), d(i)), com(c(i), a(i)));
            productTest(i, com(c(i), a(i)), com(d(i), b(i)));
            productTest(i, com(c(i), b(i)), com(d(i), a(i)));
        }
    }
}
