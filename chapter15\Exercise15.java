package chapter15;

import java.util.*;

/**
 * Exercise 15: Create a class with a method that takes a vararg argument and
 * prints each element. Experiment with different types of arguments.
 * Does autoboxing work on array elements?
 */

class VarArgDemo {
    // Method that takes varargs of Object
    public static void printObjects(Object... args) {
        System.out.println("Number of arguments: " + args.length);
        for(Object arg : args) {
            System.out.println(arg + " (Type: " + 
                (arg != null ? arg.getClass().getSimpleName() : "null") + ")");
        }
        System.out.println();
    }
    
    // Method that takes varargs of String
    public static void printStrings(String... args) {
        System.out.println("Number of String arguments: " + args.length);
        for(String arg : args) {
            System.out.println(arg);
        }
        System.out.println();
    }
    
    // Generic method that takes varargs
    public static <T> void printGeneric(T... args) {
        System.out.println("Number of generic arguments: " + args.length);
        System.out.println("Type of array: " + 
            (args.getClass().getComponentType().getSimpleName()));
        for(T arg : args) {
            System.out.println(arg + " (Type: " + 
                (arg != null ? arg.getClass().getSimpleName() : "null") + ")");
        }
        System.out.println();
    }
    
    // Method that takes varargs of primitive int
    public static void printInts(int... args) {
        System.out.println("Number of int arguments: " + args.length);
        for(int arg : args) {
            System.out.println(arg);
        }
        System.out.println();
    }
}

public class Exercise15 {
    public static void main(String[] args) {
        // Test with different types
        System.out.println("Testing printObjects with mixed types:");
        VarArgDemo.printObjects("String", 42, 3.14, true, new StringBuilder("Builder"));
        
        // Test with array
        System.out.println("Testing printObjects with array:");
        String[] stringArray = {"one", "two", "three"};
        VarArgDemo.printObjects(stringArray); // Whole array is treated as a single argument
        VarArgDemo.printObjects((Object[])stringArray); // Now each element is an argument
        
        // Test with String varargs
        System.out.println("Testing printStrings:");
        VarArgDemo.printStrings("one", "two", "three");
        
        // Test with null
        System.out.println("Testing printObjects with null:");
        VarArgDemo.printObjects((Object)null);
        
        // Test with empty argument list
        System.out.println("Testing printObjects with no arguments:");
        VarArgDemo.printObjects();
        
        // Test generic method
        System.out.println("Testing printGeneric with Integer:");
        VarArgDemo.printGeneric(1, 2, 3);
        
        System.out.println("Testing printGeneric with String:");
        VarArgDemo.printGeneric("one", "two", "three");
        
        System.out.println("Testing printGeneric with mixed types:");
        // This will use Object as the common type
        VarArgDemo.printGeneric("String", 42, 3.14, true);
        
        // Testing autoboxing with varargs
        System.out.println("Testing autoboxing with varargs:");
        VarArgDemo.printInts(1, 2, 3);
        
        // Create Integer array
        Integer[] integerArray = {1, 2, 3};
        
        System.out.println("Testing with Integer array (without autoboxing):");
        VarArgDemo.printGeneric(integerArray); // Works fine
        
        System.out.println("Testing with Integer array to int varargs (not possible directly):");
        // VarArgDemo.printInts(integerArray); // Won't compile
        
        // Convert Integer[] to int[] manually
        int[] primitiveArray = new int[integerArray.length];
        for(int i = 0; i < integerArray.length; i++) {
            primitiveArray[i] = integerArray[i]; // Unboxing works here
        }
        VarArgDemo.printInts(primitiveArray); // Works fine
        
        System.out.println("Conclusion about autoboxing:");
        System.out.println("- Autoboxing works for individual arguments in varargs methods");
        System.out.println("- Autoboxing does NOT work for entire arrays (Integer[] → int...)");
    }
} 