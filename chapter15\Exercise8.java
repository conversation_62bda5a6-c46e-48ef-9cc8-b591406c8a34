package chapter15;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

/**
 * Exercise 8: Following the form of the Coffee example, create a hierarchy
 * of StoryCharacters, then create a generator for them so that
 * next() returns a random StoryCharacter.
 */

// Base class for story characters
class StoryCharacter {
    private static long counter = 0;
    private final long id = counter++;
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + " " + id;
    }
}

// Different types of story characters
class Hero extends StoryCharacter {}
class Villain extends Story<PERSON>haracter {}
class Sidekick extends StoryCharacter {}
class Mentor extends StoryCharacter {}
class LoveInterest extends Story<PERSON>haracter {}

// Generator interface
interface Generator<T> {
    T next();
}

// StoryCharacter generator
class StoryCharacterGenerator implements Generator<StoryCharacter> {
    private Random rand = new Random();
    private Class<?>[] types = { 
        Hero.class, 
        Villain.class, 
        Sidekick.class,
        Mentor.class,
        LoveInterest.class
    };
    
    @Override
    public StoryCharacter next() {
        try {
            return (<PERSON><PERSON>hara<PERSON>)types[rand.nextInt(types.length)].newInstance();
        } catch(Exception e) {
            throw new RuntimeException(e);
        }
    }
}

// Iterable adapter for StoryCharacterGenerator
class StoryCharacterIterable implements Iterable<StoryCharacter> {
    private StoryCharacterGenerator gen = new StoryCharacterGenerator();
    private int size;
    
    public StoryCharacterIterable(int sz) {
        size = sz;
    }
    
    @Override
    public Iterator<StoryCharacter> iterator() {
        return new Iterator<StoryCharacter>() {
            private int count = size;
            
            @Override
            public boolean hasNext() {
                return count > 0;
            }
            
            @Override
            public StoryCharacter next() {
                count--;
                return gen.next();
            }
            
            @Override
            public void remove() {
                throw new UnsupportedOperationException();
            }
        };
    }
}

public class Exercise8 {
    public static void main(String[] args) {
        // Generate 10 random story characters
        StoryCharacterGenerator generator = new StoryCharacterGenerator();
        System.out.println("Generating 10 random story characters:");
        for(int i = 0; i < 10; i++) {
            System.out.println(generator.next());
        }
        
        // Use the iterable version
        System.out.println("\nUsing StoryCharacterIterable:");
        for(StoryCharacter c : new StoryCharacterIterable(10)) {
            System.out.println(c);
        }
        
        // Create a list of characters
        System.out.println("\nCreating a list of 10 characters:");
        List<StoryCharacter> characters = new ArrayList<>();
        for(int i = 0; i < 10; i++) {
            characters.add(generator.next());
        }
        
        // Count character types
        int heroes = 0, villains = 0, sidekicks = 0, mentors = 0, loveInterests = 0;
        for(StoryCharacter c : characters) {
            if(c instanceof Hero) heroes++;
            else if(c instanceof Villain) villains++;
            else if(c instanceof Sidekick) sidekicks++;
            else if(c instanceof Mentor) mentors++;
            else if(c instanceof LoveInterest) loveInterests++;
        }
        
        System.out.println("Character distribution:");
        System.out.println("Heroes: " + heroes);
        System.out.println("Villains: " + villains);
        System.out.println("Sidekicks: " + sidekicks);
        System.out.println("Mentors: " + mentors);
        System.out.println("Love Interests: " + loveInterests);
    }
} 