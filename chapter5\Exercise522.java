/**
 * 练习22：编写一个switch语句，对前面定义的各种纸币中的面值进行操作。
 * 对每种情况，打印出一些适当的文字。
 */
public class Exercise522 {
    // 复用前一个练习的枚举类型
    enum Currency {
        ONE(1),        // 1元
        FIVE(5),       // 5元
        TEN(10),       // 10元
        TWENTY(20),    // 20元
        FIFTY(50),     // 50元
        HUNDRED(100),  // 100元
        THOUSAND(1000); // 1000元
        
        private final int value;
        
        Currency(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    // 使用switch处理不同面值
    public static void describeCurrency(Currency currency) {
        System.out.print(currency + " (" + currency.getValue() + "元): ");
        
        // 使用switch语句对不同面值进行操作
        switch(currency) {
            case ONE:
                System.out.println("这是最小面额的纸币，通常用于零钱。");
                break;
            case FIVE:
                System.out.println("五元纸币，常用于日常小额支付。");
                break;
            case TEN:
                System.out.println("十元纸币，是最常见的面值之一。");
                break;
            case TWENTY:
                System.out.println("二十元纸币，适合中等金额的交易。");
                break;
            case FIFTY:
                System.out.println("五十元纸币，较高面值，用于较大额度支付。");
                break;
            case HUNDRED:
                System.out.println("一百元纸币，适合大额交易。");
                break;
            case THOUSAND:
                System.out.println("一千元纸币，最大面值，适合大额交易。");
                break;
            default:
                System.out.println("未知面值");
                break;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("使用switch语句处理枚举 - 纸币面值");
        System.out.println("----------------------------------------");
        
        // 遍历所有面值并使用switch处理
        for(Currency c : Currency.values()) {
            describeCurrency(c);
        }
        
        // 直接对特定枚举值使用switch
        System.out.println("\n单独处理特定面值:");
        describeCurrency(Currency.FIFTY);
        
        System.out.println("\n补充说明:");
        System.out.println("1. 枚举类型非常适合在switch语句中使用");
        System.out.println("2. switch语句的case标签必须是枚举类型的常量");
        System.out.println("3. 由于枚举是有限集合，switch能确保处理所有可能的情况");
    }
} 