// 练习9：创建一个Root类，它包含Component1、Component2和Component3类型的实例
// （也由你创建）。派生出一个类Stem，它也包含这三种类型的实例。所有的类都应当有
// 默认构造器，输出自己的信息。

package chapter7;

class Component1 {
    Component1() {
        System.out.println("Component1 constructor");
    }
}

class Component2 {
    Component2() {
        System.out.println("Component2 constructor");
    }
}

class Component3 {
    Component3() {
        System.out.println("Component3 constructor");
    }
}

class Root {
    Component1 c1 = new Component1();
    Component2 c2 = new Component2();
    Component3 c3 = new Component3();
    
    Root() {
        System.out.println("Root constructor");
    }
}

class Stem extends Root {
    Component1 c1 = new Component1();
    Component2 c2 = new Component2();
    Component3 c3 = new Component3();
    
    Stem() {
        System.out.println("Stem constructor");
    }
}

public class Exercise79 {
    public static void main(String[] args) {
        Stem s = new Stem();
    }
} 