package chapter11;

// 练习1：创建一个ArrayList集合，添加不同类型的元素，并演示各种操作方法

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class Exercise111 {
    public static void main(String[] args) {
        // 创建ArrayList并添加元素
        ArrayList<String> fruits = new ArrayList<>();
        System.out.println("1. 添加元素到ArrayList：");
        fruits.add("Apple");
        fruits.add("Banana");
        fruits.add("Orange");
        fruits.add("Mango");
        System.out.println("初始水果列表: " + fruits);
        
        // 在特定位置插入元素
        System.out.println("\n2. 在特定位置插入元素：");
        fruits.add(1, "Strawberry");
        System.out.println("插入后的列表: " + fruits);
        
        // 获取元素
        System.out.println("\n3. 获取元素：");
        System.out.println("索引2的元素是: " + fruits.get(2));
        
        // 修改元素
        System.out.println("\n4. 修改元素：");
        fruits.set(0, "Green Apple");
        System.out.println("修改后的列表: " + fruits);
        
        // 删除元素
        System.out.println("\n5. 删除元素：");
        fruits.remove("Mango");
        System.out.println("删除特定元素后: " + fruits);
        fruits.remove(1);
        System.out.println("删除索引1的元素后: " + fruits);
        
        // 查找元素
        System.out.println("\n6. 查找元素：");
        System.out.println("Banana在列表中的位置: " + fruits.indexOf("Banana"));
        System.out.println("列表是否包含Orange: " + fruits.contains("Orange"));
        
        // 列表大小
        System.out.println("\n7. 列表信息：");
        System.out.println("列表大小: " + fruits.size());
        System.out.println("列表是否为空: " + fruits.isEmpty());
        
        // 排序
        System.out.println("\n8. 排序：");
        Collections.sort(fruits);
        System.out.println("排序后的列表: " + fruits);
        
        // 清空列表
        System.out.println("\n9. 清空列表：");
        fruits.clear();
        System.out.println("清空后的列表: " + fruits);
        System.out.println("列表是否为空: " + fruits.isEmpty());
        
        // 使用addAll添加多个元素
        System.out.println("\n10. 使用addAll添加多个元素：");
        List<String> moreFruits = Arrays.asList("Peach", "Pear", "Plum");
        fruits.addAll(moreFruits);
        System.out.println("添加后的列表: " + fruits);
    }
} 