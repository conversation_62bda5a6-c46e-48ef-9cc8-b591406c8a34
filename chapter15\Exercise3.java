package chapter15;

/**
 * Exercise 3: Create and test a SixTuple generic.
 */

// Generic tuple classes
class TwoTuple<A, B> {
    public final A first;
    public final B second;
    
    public TwoTuple(A a, B b) {
        first = a;
        second = b;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ")";
    }
}

class ThreeTuple<A, B, C> extends TwoTuple<A, B> {
    public final C third;
    
    public ThreeTuple(A a, B b, C c) {
        super(a, b);
        third = c;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ")";
    }
}

class FourTuple<A, B, C, D> extends ThreeTuple<A, B, C> {
    public final D fourth;
    
    public FourTuple(A a, B b, C c, D d) {
        super(a, b, c);
        fourth = d;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + fourth + ")";
    }
}

class FiveTuple<A, B, C, D, E> extends FourTuple<A, B, C, D> {
    public final E fifth;
    
    public FiveTuple(A a, B b, C c, D d, E e) {
        super(a, b, c, d);
        fifth = e;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + fourth + ", " + fifth + ")";
    }
}

// New SixTuple class
class SixTuple<A, B, C, D, E, F> extends FiveTuple<A, B, C, D, E> {
    public final F sixth;
    
    public SixTuple(A a, B b, C c, D d, E e, F f) {
        super(a, b, c, d, e);
        sixth = f;
    }
    
    @Override
    public String toString() {
        return "(" + first + ", " + second + ", " + third + ", " + 
               fourth + ", " + fifth + ", " + sixth + ")";
    }
}

public class Exercise3 {
    public static void main(String[] args) {
        // Test the SixTuple with different types
        SixTuple<String, Integer, Double, Character, Boolean, Float> tuple = 
            new SixTuple<>("Hello", 42, 3.14, 'A', true, 2.71f);
        
        System.out.println("SixTuple contents: " + tuple);
        
        // Access individual elements
        System.out.println("First element (String): " + tuple.first);
        System.out.println("Second element (Integer): " + tuple.second);
        System.out.println("Third element (Double): " + tuple.third);
        System.out.println("Fourth element (Character): " + tuple.fourth);
        System.out.println("Fifth element (Boolean): " + tuple.fifth);
        System.out.println("Sixth element (Float): " + tuple.sixth);
        
        // Another example with different types
        SixTuple<Integer, Integer, Integer, Integer, Integer, Integer> intTuple =
            new SixTuple<>(1, 2, 3, 4, 5, 6);
        System.out.println("\nInteger SixTuple: " + intTuple);
    }
} 