package chapter11;

// 练习2：使用HashSet集合，演示其无序不重复的特性以及常用操作

import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class Exercise112 {
    public static void main(String[] args) {
        // 创建HashSet
        HashSet<String> colors = new HashSet<>();
        
        // 添加元素
        System.out.println("1. 添加元素到HashSet：");
        colors.add("Red");
        colors.add("Green");
        colors.add("Blue");
        colors.add("Yellow");
        colors.add("Purple");
        System.out.println("颜色集合: " + colors);
        
        // 添加重复元素
        System.out.println("\n2. 添加重复元素：");
        boolean added = colors.add("Red");
        System.out.println("是否添加成功: " + added);
        System.out.println("添加重复元素后的集合: " + colors);
        
        // 检查元素是否存在
        System.out.println("\n3. 检查元素是否存在：");
        System.out.println("集合包含'Green'? " + colors.contains("Green"));
        System.out.println("集合包含'Black'? " + colors.contains("Black"));
        
        // 删除元素
        System.out.println("\n4. 删除元素：");
        colors.remove("Yellow");
        System.out.println("删除'Yellow'后的集合: " + colors);
        
        // 集合大小
        System.out.println("\n5. 集合大小：");
        System.out.println("集合大小: " + colors.size());
        
        // 迭代集合
        System.out.println("\n6. 使用迭代器遍历集合：");
        Iterator<String> iterator = colors.iterator();
        while (iterator.hasNext()) {
            System.out.println("颜色: " + iterator.next());
        }
        
        // 使用增强for循环遍历
        System.out.println("\n7. 使用增强for循环遍历集合：");
        for (String color : colors) {
            System.out.println("颜色: " + color);
        }
        
        // 创建另一个集合
        System.out.println("\n8. 集合运算：");
        HashSet<String> additionalColors = new HashSet<>(Arrays.asList("Green", "Cyan", "Black", "White"));
        System.out.println("新集合: " + additionalColors);
        
        // 并集
        HashSet<String> union = new HashSet<>(colors);
        union.addAll(additionalColors);
        System.out.println("并集: " + union);
        
        // 交集
        HashSet<String> intersection = new HashSet<>(colors);
        intersection.retainAll(additionalColors);
        System.out.println("交集: " + intersection);
        
        // 差集
        HashSet<String> difference = new HashSet<>(colors);
        difference.removeAll(additionalColors);
        System.out.println("差集 (colors - additionalColors): " + difference);
        
        // 清空集合
        System.out.println("\n9. 清空集合：");
        colors.clear();
        System.out.println("清空后的集合: " + colors);
        System.out.println("集合是否为空: " + colors.isEmpty());
    }
} 