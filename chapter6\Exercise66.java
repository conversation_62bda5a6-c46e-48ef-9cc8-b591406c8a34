// 练习66：创建一个带有protected数据的类。在同一个文件中创建第二个类，包含一个操作第一个类的protected数据的方法。

public class Exercise66 {
    public static void main(String[] args) {
        System.out.println("练习66：从另一个类操作protected数据。");
        
        Manipulator manipulator = new Manipulator();
        manipulator.manipulate();
    }
}

class ClassWithProtectedData {
    protected int protectedValue = 10;
}

class Manipulator {
    public void manipulate() {
        ClassWithProtectedData obj = new ClassWithProtectedData();
        
        // 访问和修改protected数据
        System.out.println("原始protected值: " + obj.protectedValue);
        obj.protectedValue = 20;
        System.out.println("修改后的protected值: " + obj.protectedValue);
    }
} 