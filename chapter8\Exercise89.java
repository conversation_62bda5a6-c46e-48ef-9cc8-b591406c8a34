// 练习9：创建一个基类Instrument和派生类Wind, Percussion, Stringed, Brass和Woodwind。
// 在Instrument中声明一个play()方法，然后重载该方法，以接受一个注脚值。在Instrument中定义what()和adjust()等方法，
// 然后在派生类中覆盖这些方法。通过upcasting到Instrument创建一个乐队并演奏。


class Instrument {
    public void play(int note) {
        System.out.println("Instrument.play() " + note);
    }
    
    public String what() {
        return "Instrument";
    }
    
    public void adjust() {
        System.out.println("Adjusting Instrument");
    }
}

class Wind extends Instrument {
    @Override
    public void play(int note) {
        System.out.println("Wind.play() " + note);
    }
    
    @Override
    public String what() {
        return "Wind";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Wind");
    }
}

class Percussion extends Instrument {
    @Override
    public void play(int note) {
        System.out.println("Percussion.play() " + note);
    }
    
    @Override
    public String what() {
        return "Percussion";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Percussion");
    }
}

class Stringed extends Instrument {
    @Override
    public void play(int note) {
        System.out.println("Stringed.play() " + note);
    }
    
    @Override
    public String what() {
        return "Stringed";
    }
    
    @Override
    public void adjust() {
        System.out.println("Adjusting Stringed");
    }
}

class Brass extends Wind {
    @Override
    public void play(int note) {
        System.out.println("Brass.play() " + note);
    }
    
    @Override
    public String what() {
        return "Brass";
    }
}

class Woodwind extends Wind {
    @Override
    public void play(int note) {
        System.out.println("Woodwind.play() " + note);
    }
    
    @Override
    public String what() {
        return "Woodwind";
    }
}

public class Exercise89 {
    // Doesn't care about type, so new types
    // added to the system still work right:
    static void tune(Instrument i) {
        i.play(Notes.MIDDLE_C);
    }
    
    static void tuneAll(Instrument[] e) {
        for(Instrument i : e)
            tune(i);
    }
    
    private static class Notes {
        static final int MIDDLE_C = 0;
    }
    
    public static void main(String[] args) {
        // Upcasting during addition to the array:
        Instrument[] orchestra = {
            new Wind(),
            new Percussion(),
            new Stringed(),
            new Brass(),
            new Woodwind()
        };
        
        tuneAll(orchestra);
        
        for(Instrument i : orchestra) {
            System.out.println(i.what());
            i.adjust();
        }
    }
} 