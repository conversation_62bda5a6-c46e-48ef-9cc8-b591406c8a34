/**
 * 练习2：创建一个类，它包含一个在定义时就被初始化了的String域，
 * 以及另一个通过构造器初始化的String域。这两种方式有何差异？
 */
class Test2 {
    String s1 = "直接初始化"; // 在定义时初始化
    String s2; // 将通过构造器初始化

    Test2(String s2) {
        this.s2 = s2;
    }

//    Test2(String str) {
//        s2 = str; // 通过构造器初始化
//    }
}

public class Exercise52 {
    public static void main(String[] args) {
        Test2 t = new Test2("构造器初始化");
        System.out.println("s1 = " + t.s1);
        System.out.println("s2 = " + t.s2);
        
        // 差异：
        // 1. s1在对象创建前初始化为"直接初始化"
        // 2. s2先被初始化为null，然后在构造器中被设置为"构造器初始化"
        // 3. s1的初始值对所有对象都相同，而s2可以为每个对象设置不同的值
    }
} 