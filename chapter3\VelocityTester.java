//E4
class VelocityCalculator {
	static float velocity (float d, float t) {
		if(t == 0) {
			return 0f;
		}
		else {
			return d/t;
		}
	} 
}

public class VelocityTester {
	public static void main(String[] args) {
		float d = 1.1f;
		float t = 2.2f;
		System.out.println("Distance: " + d);
		System.out.println("Time: " + t);
		float v = VelocityCalculator.velocity(d, t);
		System.out.println("Velocity: " + v);		
	}
}