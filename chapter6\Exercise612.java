// 练习612：创建一个新的包，并在其中创建一个具有protected字段和方法的类。在另一个包中创建一个类，并尝试使用这些protected方法。解释发生了什么。

import p1.ClassA;

public class Exercise612 {
    public static void main(String[] args) {
        System.out.println("示例1：直接访问protected成员");
        ClassA a = new ClassA();
        
        // 尝试直接访问protected成员
        // a.protectedMethod(); // 编译错误：protectedMethod() 在 ClassA 中具有protected访问权限
        // System.out.println(a.protectedField); // 编译错误：protectedField 在 ClassA 中具有protected访问权限
        
        System.out.println("\n示例2：通过子类访问protected成员");
        SubClassA sub = new SubClassA();
        sub.accessProtectedMembers(); // 通过子类可以访问
        
        System.out.println("\n结论：从不同包中无法直接访问protected成员，但可以通过继承后在子类中访问");
    }
} 