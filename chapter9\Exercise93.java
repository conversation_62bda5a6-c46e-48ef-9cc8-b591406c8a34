// Exercise 3: Create an interface with at least one method, and implement 
// that interface by defining an inner class within a method, which returns 
// a reference to your interface.

interface Service3 {
    void doService();
}

public class Exercise93 {
    public Service3 getService() {
        // Anonymous inner class inside a method:
        return new Service3() {
            @Override
            public void doService() {
                System.out.println("Service performed by anonymous inner class");
            }
        };
    }
    
    public Service3 getNamedService() {
        // Named inner class inside a method:
        class ServiceImpl implements Service3 {
            @Override
            public void doService() {
                System.out.println("Service performed by named inner class");
            }
        }
        return new ServiceImpl();
    }
    
    public static void main(String[] args) {
        Exercise93 ex = new Exercise93();
        
        Service3 s1 = ex.getService();
        s1.doService();
        
        Service3 s2 = ex.getNamedService();
        s2.doService();
        
        System.out.println("Exercise 3 completed successfully");
    }
} 