package chapter15;

/**
 * Exercise 4: "Generify" the inner class sequence from the Holding Your Objects chapter.
 */

// Generic Sequence class
class Sequence<T> {
    private Object[] items;
    private int next = 0;
    
    public Sequence(int size) {
        items = new Object[size];
    }
    
    public void add(T x) {
        if(next < items.length) {
            items[next++] = x;
        }
    }
    
    private class SequenceSelector implements Selector<T> {
        private int i = 0;
        
        @Override
        public boolean end() {
            return i == items.length;
        }
        
        @SuppressWarnings("unchecked")
        @Override
        public T current() {
            return (T)items[i];
        }
        
        @Override
        public void next() {
            if(i < items.length) i++;
        }
    }
    
    public Selector<T> selector() {
        return new SequenceSelector();
    }
}

// Generic Selector interface
interface Selector<T> {
    boolean end();
    T current();
    void next();
}

public class Exercise4 {
    public static void main(String[] args) {
        // Test with String type
        Sequence<String> sequence = new Sequence<>(10);
        for(int i = 0; i < 10; i++) {
            sequence.add("String " + i);
        }
        
        Selector<String> selector = sequence.selector();
        while(!selector.end()) {
            System.out.print(selector.current() + " ");
            selector.next();
        }
        System.out.println();
        
        // Test with Integer type
        Sequence<Integer> numSequence = new Sequence<>(10);
        for(int i = 0; i < 10; i++) {
            numSequence.add(i);
        }
        
        Selector<Integer> numSelector = numSequence.selector();
        while(!numSelector.end()) {
            System.out.print(numSelector.current() + " ");
            numSelector.next();
        }
        System.out.println();
        
        // Test with a custom class
        class Item {
            private String name;
            public Item(String name) { this.name = name; }
            @Override public String toString() { return name; }
        }
        
        Sequence<Item> itemSequence = new Sequence<>(5);
        for(int i = 0; i < 5; i++) {
            itemSequence.add(new Item("Item " + i));
        }
        
        Selector<Item> itemSelector = itemSequence.selector();
        while(!itemSelector.end()) {
            System.out.print(itemSelector.current() + " ");
            itemSelector.next();
        }
    }
} 